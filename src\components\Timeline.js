import React from 'react';
import { 
  Phone, 
  Search, 
  FileText, 
  Check<PERSON>ircle, 
  Play<PERSON>ircle, 
  Tool, 
  Brush, 
  Eye, 
  Flag,
  Clock
} from 'lucide-react';
import { timelineEvents } from '../data/dummyData';

const Timeline = () => {
  const getIcon = (iconName) => {
    const icons = {
      phone: Phone,
      search: Search,
      'file-text': FileText,
      'check-circle': CheckCircle,
      'play-circle': PlayCircle,
      tool: Tool,
      brush: Brush,
      eye: Eye,
      flag: Flag
    };
    
    const IconComponent = icons[iconName] || Clock;
    return IconComponent;
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'bg-green-500';
      case 'in_progress':
        return 'bg-blue-500';
      case 'pending':
        return 'bg-gray-300';
      default:
        return 'bg-gray-300';
    }
  };

  const getStatusTextColor = (status) => {
    switch (status) {
      case 'completed':
        return 'text-green-600';
      case 'in_progress':
        return 'text-blue-600';
      case 'pending':
        return 'text-gray-500';
      default:
        return 'text-gray-500';
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  return (
    <div className="card">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900">Project Timeline</h2>
        <span className="text-sm text-gray-500">
          {timelineEvents.filter(event => event.status === 'completed').length} of {timelineEvents.length} completed
        </span>
      </div>

      <div className="relative">
        {timelineEvents.map((event, index) => {
          const Icon = getIcon(event.icon);
          const isLast = index === timelineEvents.length - 1;
          
          return (
            <div key={event.id} className={`timeline-item ${isLast ? 'border-l-0 pb-0' : ''}`}>
              {/* Timeline dot with icon */}
              <div className={`timeline-dot ${getStatusColor(event.status)} flex items-center justify-center`}>
                <Icon className="w-2.5 h-2.5 text-white" />
              </div>
              
              {/* Event content */}
              <div className="ml-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h3 className="text-sm font-medium text-gray-900 mb-1">
                      {event.title}
                    </h3>
                    <p className="text-sm text-gray-600 mb-2">
                      {event.description}
                    </p>
                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                      <span>{formatDate(event.date)}</span>
                      <span>{event.time}</span>
                    </div>
                  </div>
                  
                  {/* Status badge */}
                  <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusTextColor(event.status)} bg-opacity-10`}>
                    {event.status === 'completed' && (
                      <>
                        <CheckCircle className="w-3 h-3 mr-1" />
                        Completed
                      </>
                    )}
                    {event.status === 'in_progress' && (
                      <>
                        <Clock className="w-3 h-3 mr-1" />
                        In Progress
                      </>
                    )}
                    {event.status === 'pending' && (
                      <>
                        <Clock className="w-3 h-3 mr-1" />
                        Pending
                      </>
                    )}
                  </span>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Progress summary */}
      <div className="mt-6 pt-6 border-t border-gray-200">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600">Overall Progress</span>
          <span className="font-medium text-gray-900">
            {Math.round((timelineEvents.filter(event => event.status === 'completed').length / timelineEvents.length) * 100)}%
          </span>
        </div>
        <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-primary-600 h-2 rounded-full transition-all duration-300"
            style={{ 
              width: `${(timelineEvents.filter(event => event.status === 'completed').length / timelineEvents.length) * 100}%` 
            }}
          ></div>
        </div>
      </div>
    </div>
  );
};

export default Timeline;
