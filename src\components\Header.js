import React from 'react';
import { useLocation } from 'react-router-dom';
import { FileText, Bell, User } from 'lucide-react';
import { projectData } from '../data/dummyData';

const Header = ({ onQuoteClick }) => {
  const location = useLocation();
  
  const getPageTitle = () => {
    switch (location.pathname) {
      case '/':
        return 'Project Dashboard';
      case '/colours':
        return 'Color Palette';
      case '/payments':
        return 'Payments';
      case '/support':
        return 'Support';
      default:
        return 'AapkaPainter';
    }
  };

  return (
    <header className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Left side - Logo and Title */}
          <div className="flex items-center space-x-4">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-gradient-to-r from-primary-500 to-primary-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">AP</span>
              </div>
            </div>
            <div className="hidden sm:block">
              <h1 className="text-xl font-semibold text-gray-900">{getPageTitle()}</h1>
              {location.pathname === '/' && (
                <p className="text-sm text-gray-500">{projectData.projectTitle}</p>
              )}
            </div>
            <div className="sm:hidden">
              <h1 className="text-lg font-semibold text-gray-900">{getPageTitle()}</h1>
            </div>
          </div>

          {/* Right side - Actions */}
          <div className="flex items-center space-x-3">
            {/* Quote Button */}
            <button
              onClick={onQuoteClick}
              className="flex items-center space-x-2 bg-primary-600 hover:bg-primary-700 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
            >
              <FileText className="w-4 h-4" />
              <span className="hidden sm:inline">Quote</span>
            </button>

            {/* Notifications */}
            <button className="relative p-2 text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-lg">
              <Bell className="w-5 h-5" />
              <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
            </button>

            {/* Profile */}
            <button className="flex items-center space-x-2 p-2 text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-lg">
              <User className="w-5 h-5" />
              <span className="hidden md:inline text-sm font-medium text-gray-700">
                {projectData.customerName}
              </span>
            </button>
          </div>
        </div>
      </div>

      {/* Mobile page title */}
      {location.pathname === '/' && (
        <div className="sm:hidden px-4 pb-3">
          <p className="text-sm text-gray-500">{projectData.projectTitle}</p>
        </div>
      )}
    </header>
  );
};

export default Header;
