import React, { useState } from 'react';
import { X, Download, FileText, Calculator, Palette, Info } from 'lucide-react';
import { quotationData, projectData } from '../data/dummyData';
import { generateQuotationPDF } from '../utils/pdfGenerator';

const QuoteModal = ({ isOpen, onClose }) => {
  const [activeTab, setActiveTab] = useState('rooms');

  if (!isOpen) return null;

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  const handleDownloadPDF = async () => {
    try {
      await generateQuotationPDF();
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Error generating PDF. Please try again.');
    }
  };

  const tabs = [
    { id: 'rooms', label: 'Room-wise', icon: Calculator },
    { id: 'summary', label: 'Summary', icon: FileText },
    { id: 'terms', label: 'Terms', icon: Info }
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Paint Quotation</h2>
            <p className="text-sm text-gray-600 mt-1">
              {quotationData.quotationNumber} • {formatDate(quotationData.date)}
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={handleDownloadPDF}
              className="flex items-center space-x-2 bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
            >
              <Download className="w-4 h-4" />
              <span>Download PDF</span>
            </button>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 rounded-lg"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>

        {/* Customer Info */}
        <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <p className="font-medium text-gray-900">Customer: {projectData.customerName}</p>
              <p className="text-gray-600">{projectData.address}</p>
            </div>
            <div className="text-right">
              <p className="font-medium text-gray-900">Valid Until: {formatDate(quotationData.validUntil)}</p>
              <p className="text-gray-600">Project: {projectData.projectTitle}</p>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-200">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 px-6 py-3 text-sm font-medium border-b-2 transition-colors duration-200 ${
                  activeTab === tab.id
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-96">
          {activeTab === 'rooms' && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Room-wise Breakdown</h3>
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-2 font-medium text-gray-900">Room</th>
                      <th className="text-right py-2 font-medium text-gray-900">Area</th>
                      <th className="text-right py-2 font-medium text-gray-900">Rate</th>
                      <th className="text-right py-2 font-medium text-gray-900">Amount</th>
                      <th className="text-left py-2 font-medium text-gray-900">Paint Type</th>
                      <th className="text-left py-2 font-medium text-gray-900">Color</th>
                    </tr>
                  </thead>
                  <tbody>
                    {quotationData.rooms.map((room) => (
                      <tr key={room.id} className="border-b border-gray-100">
                        <td className="py-3 font-medium text-gray-900">{room.name}</td>
                        <td className="py-3 text-right text-gray-600">
                          {room.area} {room.unit}
                        </td>
                        <td className="py-3 text-right text-gray-600">
                          {formatCurrency(room.rate)}/{room.unit}
                        </td>
                        <td className="py-3 text-right font-medium text-gray-900">
                          {formatCurrency(room.amount)}
                        </td>
                        <td className="py-3 text-gray-600">{room.paintType}</td>
                        <td className="py-3">
                          <div className="flex items-center space-x-2">
                            <div 
                              className="w-4 h-4 rounded border border-gray-300"
                              style={{ backgroundColor: room.color === 'Warm White' ? '#F8F6F0' : 
                                                      room.color === 'Light Blue' ? '#E3F2FD' :
                                                      room.color === 'Soft Green' ? '#E8F5E8' :
                                                      room.color === 'Cream' ? '#FFF8DC' :
                                                      room.color === 'Light Yellow' ? '#FFFACD' :
                                                      '#FFFFFF' }}
                            ></div>
                            <span className="text-gray-600">{room.color}</span>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {activeTab === 'summary' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900">Cost Summary</h3>
              
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Subtotal (Painting)</span>
                    <span className="font-medium">{formatCurrency(quotationData.rooms.reduce((sum, room) => sum + room.amount, 0))}</span>
                  </div>
                  
                  {quotationData.additionalCharges.map((charge) => (
                    <div key={charge.id} className="flex justify-between text-sm">
                      <span className="text-gray-600">{charge.description}</span>
                      <span className="font-medium">{formatCurrency(charge.amount)}</span>
                    </div>
                  ))}
                  
                  <div className="border-t border-gray-200 pt-2 mt-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Subtotal</span>
                      <span className="font-medium">{formatCurrency(quotationData.subtotal)}</span>
                    </div>
                    <div className="flex justify-between text-sm text-green-600">
                      <span>Discount</span>
                      <span>-{formatCurrency(quotationData.discount)}</span>
                    </div>
                  </div>
                  
                  <div className="border-t border-gray-300 pt-2 mt-2">
                    <div className="flex justify-between text-lg font-bold">
                      <span className="text-gray-900">Total Amount</span>
                      <span className="text-primary-600">{formatCurrency(quotationData.total)}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                <div className="bg-blue-50 rounded-lg p-4">
                  <p className="text-2xl font-bold text-blue-600">{quotationData.rooms.length}</p>
                  <p className="text-sm text-blue-700">Rooms</p>
                </div>
                <div className="bg-green-50 rounded-lg p-4">
                  <p className="text-2xl font-bold text-green-600">
                    {quotationData.rooms.reduce((sum, room) => sum + room.area, 0)}
                  </p>
                  <p className="text-sm text-green-700">Total Sq Ft</p>
                </div>
                <div className="bg-purple-50 rounded-lg p-4">
                  <p className="text-2xl font-bold text-purple-600">30</p>
                  <p className="text-sm text-purple-700">Days</p>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'terms' && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">Terms & Conditions</h3>
              <ul className="space-y-3">
                {quotationData.terms.map((term, index) => (
                  <li key={index} className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-primary-500 rounded-full mt-2 flex-shrink-0"></div>
                    <span className="text-gray-700">{term}</span>
                  </li>
                ))}
              </ul>
              
              <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <p className="text-sm text-yellow-800">
                  <strong>Note:</strong> This quotation is valid for 30 days from the date of issue. 
                  Prices may vary based on material cost fluctuations.
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default QuoteModal;
