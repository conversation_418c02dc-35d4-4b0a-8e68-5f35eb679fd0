import React, { useState } from 'react';
import { Routes, Route } from 'react-router-dom';
import Header from './components/Header';
import BottomNavigation from './components/BottomNavigation';
import HomePage from './pages/HomePage';
import ColoursPage from './pages/ColoursPage';
import PaymentsPage from './pages/PaymentsPage';
import SupportPage from './pages/SupportPage';
import QuoteModal from './components/QuoteModal';

function App() {
  const [isQuoteModalOpen, setIsQuoteModalOpen] = useState(false);

  const openQuoteModal = () => setIsQuoteModalOpen(true);
  const closeQuoteModal = () => setIsQuoteModalOpen(false);

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Header */}
      <Header onQuoteClick={openQuoteModal} />
      
      {/* Main Content */}
      <main className="flex-1 pb-20 md:pb-6">
        <Routes>
          <Route path="/" element={<HomePage onQuoteClick={openQuoteModal} />} />
          <Route path="/colours" element={<ColoursPage />} />
          <Route path="/payments" element={<PaymentsPage />} />
          <Route path="/support" element={<SupportPage />} />
        </Routes>
      </main>
      
      {/* Bottom Navigation */}
      <BottomNavigation />
      
      {/* Quote Modal */}
      <QuoteModal 
        isOpen={isQuoteModalOpen} 
        onClose={closeQuoteModal} 
      />
    </div>
  );
}

export default App;
