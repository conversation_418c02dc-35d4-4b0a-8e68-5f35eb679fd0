// Dummy data for the painting project tracker

export const projectData = {
  id: "PRJ-2024-001",
  customerName: "<PERSON><PERSON>",
  projectTitle: "3BHK Apartment Painting",
  address: "Flat 402, Green Valley Apartments, Sector 15, Gurgaon",
  startDate: "2024-01-15",
  expectedEndDate: "2024-02-15",
  status: "in_progress",
  totalAmount: 85000,
  paidAmount: 42500,
  dueAmount: 42500,
  nextPaymentDue: "2024-01-30"
};

export const timelineEvents = [
  {
    id: 1,
    title: "Project Enquiry",
    description: "Initial enquiry received from customer",
    date: "2024-01-05",
    time: "10:30 AM",
    status: "completed",
    icon: "phone"
  },
  {
    id: 2,
    title: "Site Inspection",
    description: "Site visit completed, measurements taken",
    date: "2024-01-08",
    time: "2:00 PM",
    status: "completed",
    icon: "search"
  },
  {
    id: 3,
    title: "Quotation Sent",
    description: "Detailed quotation shared with customer",
    date: "2024-01-10",
    time: "11:00 AM",
    status: "completed",
    icon: "file-text"
  },
  {
    id: 4,
    title: "Project Approved",
    description: "Customer approved the quotation and terms",
    date: "2024-01-12",
    time: "4:30 PM",
    status: "completed",
    icon: "check-circle"
  },
  {
    id: 5,
    title: "Project Started",
    description: "Painting work commenced",
    date: "2024-01-15",
    time: "9:00 AM",
    status: "completed",
    icon: "play-circle"
  },
  {
    id: 6,
    title: "Wall Preparation",
    description: "Surface preparation and primer application",
    date: "2024-01-16",
    time: "9:00 AM",
    status: "completed",
    icon: "tool"
  },
  {
    id: 7,
    title: "First Coat Applied",
    description: "Base coat painting in progress",
    date: "2024-01-20",
    time: "9:00 AM",
    status: "in_progress",
    icon: "brush"
  },
  {
    id: 8,
    title: "Second Coat",
    description: "Final coat application",
    date: "2024-01-25",
    time: "9:00 AM",
    status: "pending",
    icon: "brush"
  },
  {
    id: 9,
    title: "Quality Check",
    description: "Final inspection and touch-ups",
    date: "2024-02-10",
    time: "10:00 AM",
    status: "pending",
    icon: "eye"
  },
  {
    id: 10,
    title: "Project Completion",
    description: "Handover to customer",
    date: "2024-02-15",
    time: "5:00 PM",
    status: "pending",
    icon: "flag"
  }
];

export const paymentSchedule = [
  {
    id: 1,
    title: "Advance Payment",
    amount: 25500,
    percentage: 30,
    dueDate: "2024-01-12",
    status: "paid",
    paidDate: "2024-01-12",
    description: "Initial advance for material procurement"
  },
  {
    id: 2,
    title: "First Milestone",
    amount: 17000,
    percentage: 20,
    dueDate: "2024-01-20",
    status: "paid",
    paidDate: "2024-01-20",
    description: "After wall preparation completion"
  },
  {
    id: 3,
    title: "Second Milestone",
    amount: 25500,
    percentage: 30,
    dueDate: "2024-01-30",
    status: "due",
    description: "After first coat completion"
  },
  {
    id: 4,
    title: "Final Payment",
    amount: 17000,
    percentage: 20,
    dueDate: "2024-02-15",
    status: "pending",
    description: "Upon project completion"
  }
];

export const quotationData = {
  quotationNumber: "QUO-2024-001",
  date: "2024-01-10",
  validUntil: "2024-02-10",
  rooms: [
    {
      id: 1,
      name: "Living Room",
      area: 400,
      unit: "sqft",
      rate: 45,
      amount: 18000,
      paintType: "Premium Emulsion",
      color: "Warm White",
      coats: 2
    },
    {
      id: 2,
      name: "Master Bedroom",
      area: 300,
      unit: "sqft",
      rate: 45,
      amount: 13500,
      paintType: "Premium Emulsion",
      color: "Light Blue",
      coats: 2
    },
    {
      id: 3,
      name: "Bedroom 2",
      area: 250,
      unit: "sqft",
      rate: 45,
      amount: 11250,
      paintType: "Premium Emulsion",
      color: "Soft Green",
      coats: 2
    },
    {
      id: 4,
      name: "Bedroom 3",
      area: 200,
      unit: "sqft",
      rate: 45,
      amount: 9000,
      paintType: "Premium Emulsion",
      color: "Cream",
      coats: 2
    },
    {
      id: 5,
      name: "Kitchen",
      area: 150,
      unit: "sqft",
      rate: 55,
      amount: 8250,
      paintType: "Washable Paint",
      color: "Light Yellow",
      coats: 2
    },
    {
      id: 6,
      name: "Bathrooms (2)",
      area: 120,
      unit: "sqft",
      rate: 60,
      amount: 7200,
      paintType: "Waterproof Paint",
      color: "White",
      coats: 2
    },
    {
      id: 7,
      name: "Ceiling",
      area: 800,
      unit: "sqft",
      rate: 35,
      amount: 28000,
      paintType: "Ceiling Paint",
      color: "Pure White",
      coats: 1
    }
  ],
  additionalCharges: [
    {
      id: 1,
      description: "Material Transportation",
      amount: 2000
    },
    {
      id: 2,
      description: "Surface Preparation",
      amount: 3000
    },
    {
      id: 3,
      description: "Furniture Moving",
      amount: 1500
    }
  ],
  subtotal: 95200,
  discount: 10200,
  total: 85000,
  terms: [
    "All materials included in the quotation",
    "2 coats of paint as specified",
    "Surface preparation included",
    "1 year warranty on workmanship",
    "Payment as per schedule",
    "Project completion in 30 days"
  ]
};

export const supportContacts = [
  {
    id: 1,
    name: "Project Manager",
    role: "Suresh Sharma",
    phone: "+91 98765 43210",
    email: "<EMAIL>",
    available: "Mon-Sat, 9 AM - 7 PM"
  },
  {
    id: 2,
    name: "Customer Support",
    role: "Help Desk",
    phone: "+91 1800 123 456",
    email: "<EMAIL>",
    available: "24/7"
  },
  {
    id: 3,
    name: "Emergency Contact",
    role: "Site Supervisor",
    phone: "+91 87654 32109",
    email: "<EMAIL>",
    available: "24/7"
  }
];

export const colorPalette = [
  {
    id: 1,
    name: "Warm White",
    code: "#F8F6F0",
    category: "Neutrals",
    room: "Living Room"
  },
  {
    id: 2,
    name: "Light Blue",
    code: "#E3F2FD",
    category: "Blues",
    room: "Master Bedroom"
  },
  {
    id: 3,
    name: "Soft Green",
    code: "#E8F5E8",
    category: "Greens",
    room: "Bedroom 2"
  },
  {
    id: 4,
    name: "Cream",
    code: "#FFF8DC",
    category: "Neutrals",
    room: "Bedroom 3"
  },
  {
    id: 5,
    name: "Light Yellow",
    code: "#FFFACD",
    category: "Yellows",
    room: "Kitchen"
  },
  {
    id: 6,
    name: "Pure White",
    code: "#FFFFFF",
    category: "Whites",
    room: "Ceiling & Bathrooms"
  }
];
