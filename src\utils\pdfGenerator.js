import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { quotationData, projectData } from '../data/dummyData';

export const generateQuotationPDF = async () => {
  const pdf = new jsPDF('p', 'mm', 'a4');
  const pageWidth = pdf.internal.pageSize.getWidth();
  const pageHeight = pdf.internal.pageSize.getHeight();
  const margin = 20;
  let yPosition = margin;

  // Helper function to format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Helper function to format date
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  // Header
  pdf.setFontSize(24);
  pdf.setFont('helvetica', 'bold');
  pdf.setTextColor(14, 165, 233); // Primary blue color
  pdf.text('AapkaPainter', margin, yPosition);
  
  pdf.setFontSize(12);
  pdf.setFont('helvetica', 'normal');
  pdf.setTextColor(100, 100, 100);
  pdf.text('Professional Painting Services', margin, yPosition + 8);
  
  yPosition += 25;

  // Title
  pdf.setFontSize(20);
  pdf.setFont('helvetica', 'bold');
  pdf.setTextColor(0, 0, 0);
  pdf.text('PAINT QUOTATION', margin, yPosition);
  
  yPosition += 15;

  // Quotation details
  pdf.setFontSize(10);
  pdf.setFont('helvetica', 'normal');
  pdf.text(`Quotation No: ${quotationData.quotationNumber}`, margin, yPosition);
  pdf.text(`Date: ${formatDate(quotationData.date)}`, pageWidth - margin - 50, yPosition);
  
  yPosition += 6;
  pdf.text(`Valid Until: ${formatDate(quotationData.validUntil)}`, pageWidth - margin - 50, yPosition);
  
  yPosition += 15;

  // Customer details
  pdf.setFontSize(12);
  pdf.setFont('helvetica', 'bold');
  pdf.text('CUSTOMER DETAILS', margin, yPosition);
  
  yPosition += 8;
  pdf.setFontSize(10);
  pdf.setFont('helvetica', 'normal');
  pdf.text(`Name: ${projectData.customerName}`, margin, yPosition);
  
  yPosition += 6;
  pdf.text(`Project: ${projectData.projectTitle}`, margin, yPosition);
  
  yPosition += 6;
  const addressLines = pdf.splitTextToSize(`Address: ${projectData.address}`, pageWidth - 2 * margin);
  pdf.text(addressLines, margin, yPosition);
  
  yPosition += addressLines.length * 6 + 10;

  // Room-wise breakdown table
  pdf.setFontSize(12);
  pdf.setFont('helvetica', 'bold');
  pdf.text('ROOM-WISE BREAKDOWN', margin, yPosition);
  
  yPosition += 10;

  // Table headers
  const tableHeaders = ['Room', 'Area (sqft)', 'Rate', 'Amount', 'Paint Type'];
  const colWidths = [40, 25, 25, 25, 55];
  let xPosition = margin;

  pdf.setFontSize(9);
  pdf.setFont('helvetica', 'bold');
  pdf.setFillColor(240, 240, 240);
  pdf.rect(margin, yPosition - 5, pageWidth - 2 * margin, 8, 'F');

  tableHeaders.forEach((header, index) => {
    pdf.text(header, xPosition + 2, yPosition);
    xPosition += colWidths[index];
  });

  yPosition += 8;

  // Table rows
  pdf.setFont('helvetica', 'normal');
  quotationData.rooms.forEach((room) => {
    if (yPosition > pageHeight - 30) {
      pdf.addPage();
      yPosition = margin;
    }

    xPosition = margin;
    const rowData = [
      room.name,
      `${room.area}`,
      `₹${room.rate}`,
      formatCurrency(room.amount),
      room.paintType
    ];

    rowData.forEach((data, index) => {
      const cellText = pdf.splitTextToSize(data, colWidths[index] - 4);
      pdf.text(cellText, xPosition + 2, yPosition);
      xPosition += colWidths[index];
    });

    yPosition += 8;
  });

  yPosition += 10;

  // Additional charges
  if (quotationData.additionalCharges.length > 0) {
    pdf.setFontSize(12);
    pdf.setFont('helvetica', 'bold');
    pdf.text('ADDITIONAL CHARGES', margin, yPosition);
    
    yPosition += 8;
    pdf.setFontSize(10);
    pdf.setFont('helvetica', 'normal');

    quotationData.additionalCharges.forEach((charge) => {
      pdf.text(charge.description, margin, yPosition);
      pdf.text(formatCurrency(charge.amount), pageWidth - margin - 30, yPosition);
      yPosition += 6;
    });

    yPosition += 10;
  }

  // Cost summary
  pdf.setFontSize(12);
  pdf.setFont('helvetica', 'bold');
  pdf.text('COST SUMMARY', margin, yPosition);
  
  yPosition += 10;
  pdf.setFontSize(10);
  pdf.setFont('helvetica', 'normal');

  // Summary lines
  const summaryItems = [
    ['Subtotal', formatCurrency(quotationData.subtotal)],
    ['Discount', `-${formatCurrency(quotationData.discount)}`],
  ];

  summaryItems.forEach(([label, amount]) => {
    pdf.text(label, margin, yPosition);
    pdf.text(amount, pageWidth - margin - 30, yPosition);
    yPosition += 6;
  });

  // Total line
  pdf.setFont('helvetica', 'bold');
  pdf.setFontSize(12);
  pdf.line(margin, yPosition, pageWidth - margin, yPosition);
  yPosition += 8;
  pdf.text('TOTAL AMOUNT', margin, yPosition);
  pdf.text(formatCurrency(quotationData.total), pageWidth - margin - 30, yPosition);

  yPosition += 20;

  // Terms and conditions
  if (yPosition > pageHeight - 60) {
    pdf.addPage();
    yPosition = margin;
  }

  pdf.setFontSize(12);
  pdf.setFont('helvetica', 'bold');
  pdf.text('TERMS & CONDITIONS', margin, yPosition);
  
  yPosition += 8;
  pdf.setFontSize(9);
  pdf.setFont('helvetica', 'normal');

  quotationData.terms.forEach((term, index) => {
    const termText = `${index + 1}. ${term}`;
    const lines = pdf.splitTextToSize(termText, pageWidth - 2 * margin);
    pdf.text(lines, margin, yPosition);
    yPosition += lines.length * 5 + 2;
  });

  // Footer
  yPosition = pageHeight - 20;
  pdf.setFontSize(8);
  pdf.setTextColor(100, 100, 100);
  pdf.text('Thank you for choosing AapkaPainter!', margin, yPosition);
  pdf.text(`Generated on ${new Date().toLocaleDateString('en-IN')}`, pageWidth - margin - 40, yPosition);

  // Save the PDF
  pdf.save(`AapkaPainter_Quotation_${quotationData.quotationNumber}.pdf`);
};

export const generateQuotationFromElement = async (elementId) => {
  try {
    const element = document.getElementById(elementId);
    if (!element) {
      throw new Error('Element not found');
    }

    const canvas = await html2canvas(element, {
      scale: 2,
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff'
    });

    const imgData = canvas.toDataURL('image/png');
    const pdf = new jsPDF('p', 'mm', 'a4');
    
    const imgWidth = 210; // A4 width in mm
    const pageHeight = 295; // A4 height in mm
    const imgHeight = (canvas.height * imgWidth) / canvas.width;
    let heightLeft = imgHeight;
    let position = 0;

    pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
    heightLeft -= pageHeight;

    while (heightLeft >= 0) {
      position = heightLeft - imgHeight;
      pdf.addPage();
      pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;
    }

    pdf.save(`AapkaPainter_Quotation_${quotationData.quotationNumber}.pdf`);
  } catch (error) {
    console.error('Error generating PDF:', error);
    alert('Error generating PDF. Please try again.');
  }
};
