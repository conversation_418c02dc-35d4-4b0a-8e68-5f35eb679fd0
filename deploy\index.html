<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ShriniPaints - Professional Painting Services</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#fef2f2',
                            100: '#fee2e2',
                            200: '#fecaca',
                            300: '#fca5a5',
                            400: '#f87171',
                            500: '#ef4444',
                            600: '#dc2626',
                            700: '#b91c1c',
                            800: '#991b1b',
                            900: '#7f1d1d',
                        },
                        coral: {
                            50: '#fff5f5',
                            100: '#fed7d7',
                            200: '#feb2b2',
                            300: '#fc8181',
                            400: '#f56565',
                            500: '#e53e3e',
                            600: '#c53030',
                            700: '#9c1c1c',
                            800: '#742a2a',
                            900: '#4a1818',
                        },
                        orange: {
                            400: '#ff7849',
                            500: '#ff6b35',
                            600: '#e55a2b',
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .timeline-item {
            position: relative;
            padding-left: 2rem;
            padding-bottom: 2rem;
            border-left: 2px solid #e5e7eb;
        }
        .timeline-item:last-child {
            border-left: 0;
            padding-bottom: 0;
        }
        .timeline-dot {
            position: absolute;
            left: -0.5rem;
            top: 0;
            width: 1rem;
            height: 1rem;
            border-radius: 50%;
            border: 2px solid white;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }
        
        /* Chat Widget Styles */
        .chat-widget {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        .chat-bubble {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #ff6b6b, #ffa500);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transition: transform 0.3s ease;
        }
        
        .chat-bubble:hover {
            transform: scale(1.1);
        }
        
        .chat-window {
            position: absolute;
            bottom: 80px;
            right: 0;
            width: 350px;
            height: 500px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.2);
            display: none;
            flex-direction: column;
        }
        
        .chat-header {
            background: linear-gradient(135deg, #ff6b6b, #ffa500);
            color: white;
            padding: 16px;
            border-radius: 12px 12px 0 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .chat-messages {
            flex: 1;
            padding: 16px;
            overflow-y: auto;
            background: #f8f9fa;
            display: flex;
            flex-direction: column;
        }
        
        .message {
            margin-bottom: 12px;
            padding: 8px 12px;
            border-radius: 12px;
            max-width: 80%;
            font-size: 14px;
        }
        
        .message.bot {
            background: #e3f2fd;
            align-self: flex-start;
        }
        
        .message.user {
            background: #ff6b6b;
            color: white;
            align-self: flex-end;
        }
        
        .chat-input {
            padding: 16px;
            border-top: 1px solid #e0e0e0;
            display: flex;
            gap: 8px;
        }
        
        .typing-indicator {
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 8px 12px;
            background: #e3f2fd;
            border-radius: 12px;
            max-width: 80%;
            align-self: flex-start;
        }
        
        .typing-dot {
            width: 6px;
            height: 6px;
            background: #666;
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }
        
        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }
        
        @keyframes typing {
            0%, 60%, 100% { transform: translateY(0); }
            30% { transform: translateY(-10px); }
        }
    </style>
</head>
<body class="bg-gray-50 flex">
    <script>
        // Redirect to demo.html for now, but this could be the main content
        window.location.href = 'demo.html';
    </script>
    
    <div class="flex items-center justify-center min-h-screen w-full">
        <div class="text-center">
            <div class="w-16 h-16 bg-gradient-to-r from-orange-400 to-orange-500 rounded-lg flex items-center justify-center mx-auto mb-4">
                <span class="text-white font-bold text-xl">SP</span>
            </div>
            <h1 class="text-2xl font-bold text-gray-900 mb-2">ShriniPaints</h1>
            <p class="text-gray-600 mb-4">Loading your painting project dashboard...</p>
            <p class="text-sm text-gray-500">If not redirected, <a href="demo.html" class="text-orange-500 hover:text-orange-600">click here</a></p>
        </div>
    </div>
</body>
</html>
