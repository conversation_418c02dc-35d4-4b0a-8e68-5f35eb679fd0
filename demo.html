<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aapka<PERSON><PERSON>ter - Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .timeline-item {
            position: relative;
            padding-left: 2rem;
            padding-bottom: 2rem;
            border-left: 2px solid #e5e7eb;
        }
        .timeline-item:last-child {
            border-left: 0;
            padding-bottom: 0;
        }
        .timeline-dot {
            position: absolute;
            left: -0.5rem;
            top: 0;
            width: 1rem;
            height: 1rem;
            border-radius: 50%;
            border: 2px solid white;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-4">
                    <div class="w-8 h-8 bg-gradient-to-r from-primary-500 to-primary-600 rounded-lg flex items-center justify-center">
                        <span class="text-white font-bold text-sm">AP</span>
                    </div>
                    <div>
                        <h1 class="text-xl font-semibold text-gray-900">Project Dashboard</h1>
                        <p class="text-sm text-gray-500">3BHK Apartment Painting</p>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <button onclick="downloadPDF()" class="flex items-center space-x-2 bg-primary-600 hover:bg-primary-700 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <span>Quote</span>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 space-y-6">
        <!-- Project Overview -->
        <div class="bg-white rounded-xl shadow-sm p-6">
            <div class="flex items-start justify-between mb-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 mb-2">3BHK Apartment Painting</h1>
                    <p class="text-gray-600 text-sm">Project ID: PRJ-2024-001</p>
                </div>
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                    In Progress
                </span>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="flex items-center space-x-3">
                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                    <div>
                        <p class="text-sm font-medium text-gray-900">Customer</p>
                        <p class="text-sm text-gray-600">Rajesh Kumar</p>
                    </div>
                </div>
                <div class="flex items-start space-x-3">
                    <svg class="w-5 h-5 text-gray-400 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    <div>
                        <p class="text-sm font-medium text-gray-900">Location</p>
                        <p class="text-sm text-gray-600">Flat 402, Green Valley Apartments, Sector 15, Gurgaon</p>
                    </div>
                </div>
            </div>

            <div class="bg-gray-50 rounded-lg p-4">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-sm font-medium text-gray-700">Project Progress</span>
                    <span class="text-sm text-gray-600">60% Complete</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-gradient-to-r from-primary-500 to-primary-600 h-2 rounded-full" style="width: 60%"></div>
                </div>
                <p class="text-xs text-gray-500 mt-2">Wall preparation completed, first coat in progress</p>
            </div>
        </div>

        <!-- Payment Card -->
        <div class="bg-white rounded-xl shadow-sm p-6">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-semibold text-gray-900">Payment Overview</h2>
                <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                </svg>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="bg-gradient-to-r from-primary-50 to-primary-100 rounded-lg p-4">
                    <p class="text-sm font-medium text-primary-700 mb-1">Total Amount</p>
                    <p class="text-2xl font-bold text-primary-900">₹85,000</p>
                </div>
                <div class="bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-4">
                    <p class="text-sm font-medium text-green-700 mb-1">Paid Amount</p>
                    <p class="text-2xl font-bold text-green-900">₹42,500</p>
                    <p class="text-xs text-green-600 mt-1">50% completed</p>
                </div>
                <div class="bg-gradient-to-r from-red-50 to-red-100 rounded-lg p-4">
                    <p class="text-sm font-medium text-red-700 mb-1">Due Amount</p>
                    <p class="text-2xl font-bold text-red-900">₹42,500</p>
                    <p class="text-xs text-red-600 mt-1">Due: 30 Jan 2024</p>
                </div>
            </div>

            <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                <div class="flex items-start space-x-3">
                    <svg class="w-5 h-5 text-red-500 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                    <div class="flex-1">
                        <h3 class="text-sm font-medium text-red-800 mb-1">Payment Due</h3>
                        <p class="text-sm text-red-700 mb-2">Second Milestone - ₹25,500 (30%)</p>
                        <button onclick="payNow()" class="inline-flex items-center space-x-2 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                            </svg>
                            <span>Pay Now</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Timeline -->
        <div class="bg-white rounded-xl shadow-sm p-6">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-semibold text-gray-900">Project Timeline</h2>
                <span class="text-sm text-gray-500">6 of 10 completed</span>
            </div>

            <div class="relative">
                <div class="timeline-item">
                    <div class="timeline-dot bg-green-500"></div>
                    <div class="ml-4">
                        <h3 class="text-sm font-medium text-gray-900 mb-1">Project Enquiry</h3>
                        <p class="text-sm text-gray-600 mb-2">Initial enquiry received from customer</p>
                        <div class="flex items-center space-x-4 text-xs text-gray-500">
                            <span>5 Jan 2024</span>
                            <span>10:30 AM</span>
                            <span class="inline-flex items-center text-green-600">
                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Completed
                            </span>
                        </div>
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="timeline-dot bg-green-500"></div>
                    <div class="ml-4">
                        <h3 class="text-sm font-medium text-gray-900 mb-1">Site Inspection</h3>
                        <p class="text-sm text-gray-600 mb-2">Site visit completed, measurements taken</p>
                        <div class="flex items-center space-x-4 text-xs text-gray-500">
                            <span>8 Jan 2024</span>
                            <span>2:00 PM</span>
                            <span class="inline-flex items-center text-green-600">
                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Completed
                            </span>
                        </div>
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="timeline-dot bg-blue-500"></div>
                    <div class="ml-4">
                        <h3 class="text-sm font-medium text-gray-900 mb-1">First Coat Applied</h3>
                        <p class="text-sm text-gray-600 mb-2">Base coat painting in progress</p>
                        <div class="flex items-center space-x-4 text-xs text-gray-500">
                            <span>20 Jan 2024</span>
                            <span>9:00 AM</span>
                            <span class="inline-flex items-center text-blue-600">
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                In Progress
                            </span>
                        </div>
                    </div>
                </div>

                <div class="timeline-item">
                    <div class="timeline-dot bg-gray-300"></div>
                    <div class="ml-4">
                        <h3 class="text-sm font-medium text-gray-900 mb-1">Second Coat</h3>
                        <p class="text-sm text-gray-600 mb-2">Final coat application</p>
                        <div class="flex items-center space-x-4 text-xs text-gray-500">
                            <span>25 Jan 2024</span>
                            <span>9:00 AM</span>
                            <span class="inline-flex items-center text-gray-500">
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                Pending
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-6 pt-6 border-t border-gray-200">
                <div class="flex items-center justify-between text-sm">
                    <span class="text-gray-600">Overall Progress</span>
                    <span class="font-medium text-gray-900">60%</span>
                </div>
                <div class="mt-2 w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-primary-600 h-2 rounded-full" style="width: 60%"></div>
                </div>
            </div>
        </div>
    </main>

    <!-- Bottom Navigation (Mobile) -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 md:hidden z-50">
        <div class="flex justify-around items-center h-16">
            <a href="#" class="flex flex-col items-center justify-center py-2 px-1 text-xs font-medium text-primary-600">
                <svg class="w-5 h-5 mb-1" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
                </svg>
                <span>Home</span>
            </a>
            <a href="#" class="flex flex-col items-center justify-center py-2 px-1 text-xs font-medium text-gray-500">
                <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path>
                </svg>
                <span>Colours</span>
            </a>
            <a href="#" class="flex flex-col items-center justify-center py-2 px-1 text-xs font-medium text-gray-500">
                <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                </svg>
                <span>Payments</span>
            </a>
            <a href="#" class="flex flex-col items-center justify-center py-2 px-1 text-xs font-medium text-gray-500">
                <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span>Support</span>
            </a>
        </div>
    </nav>

    <script>
        function downloadPDF() {
            alert('PDF download functionality would be implemented here using jsPDF library');
        }

        function payNow() {
            alert('Payment gateway integration would be implemented here');
        }

        // Add some interactivity
        document.addEventListener('DOMContentLoaded', function() {
            console.log('AapkaPainter Demo Loaded Successfully!');
        });
    </script>
</body>
</html>
