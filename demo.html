<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Aapka<PERSON><PERSON>ter - Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#fef2f2',
                            100: '#fee2e2',
                            200: '#fecaca',
                            300: '#fca5a5',
                            400: '#f87171',
                            500: '#ef4444',
                            600: '#dc2626',
                            700: '#b91c1c',
                            800: '#991b1b',
                            900: '#7f1d1d',
                        },
                        coral: {
                            50: '#fff5f5',
                            100: '#fed7d7',
                            200: '#feb2b2',
                            300: '#fc8181',
                            400: '#f56565',
                            500: '#e53e3e',
                            600: '#c53030',
                            700: '#9c1c1c',
                            800: '#742a2a',
                            900: '#4a1818',
                        },
                        orange: {
                            400: '#ff7849',
                            500: '#ff6b35',
                            600: '#e55a2b',
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .timeline-item {
            position: relative;
            padding-left: 2rem;
            padding-bottom: 2rem;
            border-left: 2px solid #e5e7eb;
        }
        .timeline-item:last-child {
            border-left: 0;
            padding-bottom: 0;
        }
        .timeline-dot {
            position: absolute;
            left: -0.5rem;
            top: 0;
            width: 1rem;
            height: 1rem;
            border-radius: 50%;
            border: 2px solid white;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }

        /* Chat Widget Styles */
        .chat-widget {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }

        .chat-bubble {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #ff6b6b, #ffa500);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transition: transform 0.3s ease;
        }

        .chat-bubble:hover {
            transform: scale(1.1);
        }

        .chat-window {
            position: absolute;
            bottom: 80px;
            right: 0;
            width: 350px;
            height: 500px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.2);
            display: none;
            flex-direction: column;
        }

        .chat-header {
            background: linear-gradient(135deg, #ff6b6b, #ffa500);
            color: white;
            padding: 16px;
            border-radius: 12px 12px 0 0;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .chat-messages {
            flex: 1;
            padding: 16px;
            overflow-y: auto;
            background: #f8f9fa;
            display: flex;
            flex-direction: column;
        }

        .message {
            margin-bottom: 12px;
            padding: 8px 12px;
            border-radius: 12px;
            max-width: 80%;
            font-size: 14px;
        }

        .message.bot {
            background: #e3f2fd;
            align-self: flex-start;
        }

        .message.user {
            background: #ff6b6b;
            color: white;
            align-self: flex-end;
        }

        .chat-input {
            padding: 16px;
            border-top: 1px solid #e0e0e0;
            display: flex;
            gap: 8px;
        }

        .typing-indicator {
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 8px 12px;
            background: #e3f2fd;
            border-radius: 12px;
            max-width: 80%;
            align-self: flex-start;
        }

        .typing-dot {
            width: 6px;
            height: 6px;
            background: #666;
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }

        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }

        @keyframes typing {
            0%, 60%, 100% { transform: translateY(0); }
            30% { transform: translateY(-10px); }
        }
    </style>
</head>
<body class="bg-gray-50 flex">
    <!-- Sidebar Navigation -->
    <nav class="hidden md:flex md:flex-col md:w-64 bg-white shadow-sm border-r border-gray-200 h-screen sticky top-0">
        <div class="p-6">
            <div class="flex items-center space-x-2">
                <div class="w-8 h-8 bg-gradient-to-r from-orange-400 to-orange-500 rounded-lg flex items-center justify-center">
                    <span class="text-white font-bold text-sm">SP</span>
                </div>
                <span class="text-xl font-bold text-gray-900">ShriniPaints</span>
            </div>
        </div>

        <div class="flex-1 px-4">
            <ul class="space-y-2">
                <li>
                    <a href="#" class="flex items-center space-x-3 px-4 py-3 text-sm font-medium text-white bg-orange-500 rounded-lg">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
                        </svg>
                        <span>Home</span>
                    </a>
                </li>
                <li>
                    <a href="#" class="flex items-center space-x-3 px-4 py-3 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path>
                        </svg>
                        <span>Colours</span>
                    </a>
                </li>
                <li>
                    <a href="#" onclick="showPaymentsSection()" class="flex items-center space-x-3 px-4 py-3 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                        </svg>
                        <span>Payments</span>
                    </a>
                </li>
                <li>
                    <a href="#" onclick="toggleChatWindow()" class="flex items-center space-x-3 px-4 py-3 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-lg">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span>Support</span>
                    </a>
                </li>
            </ul>
        </div>
    </nav>

    <!-- Main Content Area -->
    <div class="flex-1 flex flex-col">
        <!-- Top Header -->
        <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40">
            <div class="px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center h-16">
                    <div class="flex items-center space-x-4">
                        <button class="md:hidden p-2 text-gray-600">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                            </svg>
                        </button>
                        <div class="md:hidden flex items-center space-x-2">
                            <div class="w-8 h-8 bg-gradient-to-r from-orange-400 to-orange-500 rounded-lg flex items-center justify-center">
                                <span class="text-white font-bold text-sm">SP</span>
                            </div>
                            <span class="text-lg font-bold text-gray-900">ShriniPaints</span>
                        </div>
                        <div class="hidden md:block">
                            <h1 class="text-xl font-semibold text-gray-900">Your Journey With Us</h1>
                        </div>
                    </div>
                    <div class="flex items-center space-x-3">
                        <button class="flex items-center space-x-2 text-gray-600 hover:text-gray-900 px-3 py-2 rounded-lg text-sm font-medium">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                            </svg>
                            <span>Filters</span>
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="flex-1 px-4 sm:px-6 lg:px-8 py-6 space-y-6 overflow-y-auto">
            <!-- Home Section -->
            <div id="homeSection">
                <!-- Payment Card - Matching ShriniPaints Design -->
            <div class="bg-gradient-to-r from-red-50 to-orange-50 rounded-xl border border-red-200 p-6">
                <div class="flex items-start justify-between mb-6">
                    <div>
                        <div class="flex items-center space-x-2 mb-2">
                            <span class="text-sm font-medium text-gray-700">PAYMENT:</span>
                            <span class="text-sm text-gray-600">Make Secured Payment</span>
                        </div>
                        <h2 class="text-xl font-bold text-gray-900">1st Milestone</h2>
                    </div>
                    <div class="bg-orange-500 text-white px-4 py-2 rounded-lg font-bold text-lg">
                        40%
                    </div>
                </div>

                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                    <div>
                        <p class="text-sm text-gray-600 mb-1">Total Project Value</p>
                        <p class="text-lg font-bold text-gray-900">₹ 50,364</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600 mb-1">Amount Paid</p>
                        <p class="text-lg font-bold text-gray-900">₹ 0</p>
                    </div>
                    <div>
                        <p class="text-sm text-orange-600 font-medium mb-1">Due Now</p>
                        <p class="text-lg font-bold text-orange-600">₹ 20,145</p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600 mb-1">Pending Upcoming Payments</p>
                        <p class="text-lg font-bold text-gray-900">₹ 30,219</p>
                    </div>
                </div>

                <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between">
                    <button onclick="payNow()" class="bg-orange-500 hover:bg-orange-600 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200 mb-2 sm:mb-0">
                        Pay Now ₹ 20,145
                    </button>
                    <p class="text-xs text-gray-600">Secure Payments | Debit/Credit Card, Netbanking, Wallets, UPI & EMI</p>
                </div>
            </div>

            <!-- Timeline Section -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                <!-- Timeline Item 1 -->
                <div class="timeline-section">
                    <div class="p-4 border-b border-gray-200 bg-gray-100 cursor-pointer" onclick="toggleTimeline('timeline1')">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <span class="text-sm text-orange-600 font-medium">Jul 11:</span>
                                <span class="text-sm text-gray-700">You have selected your project start date!</span>
                            </div>
                            <svg id="arrow1" class="w-4 h-4 text-gray-400 transform transition-transform" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                    </div>
                    <div id="timeline1" class="p-4 bg-orange-50 border-b border-gray-200">
                        <p class="text-sm text-gray-700">You have entered your project start date as <strong>Wednesday, Jul-16-2025</strong>.</p>
                    </div>
                </div>

                <!-- Timeline Item 2 -->
                <div class="timeline-section">
                    <div class="p-4 border-b border-gray-200 bg-gray-100 cursor-pointer" onclick="toggleTimeline('timeline2')">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <span class="text-sm text-orange-600 font-medium">Jul 11:</span>
                                <span class="text-sm text-gray-700">Hurray! You Have Accepted The Quote.</span>
                            </div>
                            <svg id="arrow2" class="w-4 h-4 text-gray-400 transform transition-transform" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                    </div>
                    <div id="timeline2" class="p-4 bg-orange-50 border-b border-gray-200 hidden">
                        <div class="flex items-center justify-between bg-white rounded-lg p-4 border border-orange-200">
                            <div class="flex items-center space-x-4">
                                <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                                    <span class="text-orange-600 font-bold text-sm">AP</span>
                                </div>
                                <div>
                                    <h4 class="font-medium text-gray-900">ASIAN PAINTS</h4>
                                    <p class="text-lg font-bold text-gray-900">₹ 50,364</p>
                                    <div class="flex items-center space-x-2">
                                        <span class="bg-red-600 text-white text-xs px-2 py-1 rounded-full">20% OFF</span>
                                    </div>
                                </div>
                            </div>
                            <button onclick="openQuoteModal()" class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded text-sm font-medium transition-colors">
                                View Quote
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Timeline Item 3 -->
                <div class="timeline-section">
                    <div class="p-4 border-b border-gray-200 bg-gray-100 cursor-pointer" onclick="toggleTimeline('timeline3')">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-2">
                                <span class="text-sm text-orange-600 font-medium">Jul 11:</span>
                                <span class="text-sm text-gray-700">Site Inspection</span>
                            </div>
                            <svg id="arrow3" class="w-4 h-4 text-gray-400 transform transition-transform" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                    </div>
                    <div id="timeline3" class="p-4 bg-orange-50 border-b border-gray-200 hidden">
                        <div class="flex items-center justify-between">
                            <p class="text-sm text-gray-600">Last updated at 05:08 PM On 11 Jul 2025</p>
                            <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Timeline Items -->
            <div class="bg-white rounded-xl shadow-sm p-6">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-xl font-semibold text-gray-900">Project Timeline</h2>
                    <span class="text-sm text-gray-500">6 of 10 completed</span>
                </div>

                <div class="relative">
                    <div class="timeline-item">
                        <div class="timeline-dot bg-green-500"></div>
                        <div class="ml-4">
                            <h3 class="text-sm font-medium text-gray-900 mb-1">Project Enquiry</h3>
                            <p class="text-sm text-gray-600 mb-2">Initial enquiry received from customer</p>
                            <div class="flex items-center space-x-4 text-xs text-gray-500">
                                <span>5 Jan 2024</span>
                                <span>10:30 AM</span>
                                <span class="inline-flex items-center text-green-600">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                    Completed
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="timeline-item">
                        <div class="timeline-dot bg-green-500"></div>
                        <div class="ml-4">
                            <h3 class="text-sm font-medium text-gray-900 mb-1">Site Inspection</h3>
                            <p class="text-sm text-gray-600 mb-2">Site visit completed, measurements taken</p>
                            <div class="flex items-center space-x-4 text-xs text-gray-500">
                                <span>8 Jan 2024</span>
                                <span>2:00 PM</span>
                                <span class="inline-flex items-center text-green-600">
                                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                    Completed
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="timeline-item">
                        <div class="timeline-dot bg-orange-500"></div>
                        <div class="ml-4">
                            <h3 class="text-sm font-medium text-gray-900 mb-1">First Coat Applied</h3>
                            <p class="text-sm text-gray-600 mb-2">Base coat painting in progress</p>
                            <div class="flex items-center space-x-4 text-xs text-gray-500">
                                <span>20 Jan 2024</span>
                                <span>9:00 AM</span>
                                <span class="inline-flex items-center text-orange-600">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    In Progress
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="timeline-item">
                        <div class="timeline-dot bg-gray-300"></div>
                        <div class="ml-4">
                            <h3 class="text-sm font-medium text-gray-900 mb-1">Second Coat</h3>
                            <p class="text-sm text-gray-600 mb-2">Final coat application</p>
                            <div class="flex items-center space-x-4 text-xs text-gray-500">
                                <span>25 Jan 2024</span>
                                <span>9:00 AM</span>
                                <span class="inline-flex items-center text-gray-500">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Pending
                                </span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-6 pt-6 border-t border-gray-200">
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-gray-600">Overall Progress</span>
                        <span class="font-medium text-gray-900">40%</span>
                    </div>
                    <div class="mt-2 w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-orange-500 h-2 rounded-full" style="width: 40%"></div>
                    </div>
                </div>
            </div>
            </div> <!-- End Home Section -->
        </main>
    </div>



    <!-- Bottom Navigation (Mobile) -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 md:hidden z-50">
        <div class="flex justify-around items-center h-16">
            <a href="#" class="flex flex-col items-center justify-center py-2 px-1 text-xs font-medium text-orange-600">
                <svg class="w-5 h-5 mb-1" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
                </svg>
                <span>Home</span>
            </a>
            <a href="#" class="flex flex-col items-center justify-center py-2 px-1 text-xs font-medium text-gray-500">
                <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path>
                </svg>
                <span>Colours</span>
            </a>
            <a href="#" class="flex flex-col items-center justify-center py-2 px-1 text-xs font-medium text-gray-500">
                <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                </svg>
                <span>Payments</span>
            </a>
            <a href="#" class="flex flex-col items-center justify-center py-2 px-1 text-xs font-medium text-gray-500">
                <svg class="w-5 h-5 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span>Support</span>
            </a>
        </div>
    </nav>

    <!-- Chat Widget -->
    <div class="chat-widget">
        <div class="chat-window" id="chatWindow">
            <div class="chat-header">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="font-semibold">Support Chat</h3>
                        <p class="text-sm opacity-90">Shrinivas - ShriniPaints</p>
                    </div>
                </div>
                <button onclick="closeChatWindow()" class="text-white hover:text-gray-200">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <div class="chat-messages" id="chatMessages">
                <div class="message bot">
                    Hi! I'm Shrinivas from ShriniPaints. How can I help you today? 😊
                </div>
                <div class="message bot">
                    You can ask me about:
                    <br>• Painting services & quotes
                    <br>• Project timelines
                    <br>• Payment options
                    <br>• Any other questions!
                </div>
            </div>

            <div class="chat-input">
                <input
                    type="text"
                    id="chatInput"
                    placeholder="Type your message..."
                    class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 text-sm"
                    onkeypress="handleChatKeyPress(event)"
                >
                <button
                    onclick="sendMessage()"
                    class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                >
                    Send
                </button>
            </div>
        </div>

        <div class="chat-bubble" onclick="toggleChatWindow()">
            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clip-rule="evenodd"></path>
            </svg>
        </div>
    </div>

    <!-- Quotation Modal -->
    <div id="quoteModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden mx-4">
            <!-- Modal Header -->
            <div class="flex items-center justify-between p-6 border-b border-gray-200">
                <div class="flex items-center space-x-4">
                    <h2 class="text-2xl font-bold text-gray-900">Quotation - Asian Paints</h2>
                    <div class="bg-orange-100 text-orange-600 px-3 py-1 rounded-full">
                        <span class="text-sm font-medium">₹50,364</span>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <button onclick="downloadQuotePDF()" class="flex items-center space-x-2 bg-gray-900 hover:bg-gray-800 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <span>Download</span>
                    </button>
                    <button onclick="closeQuoteModal()" class="p-2 text-gray-400 hover:text-gray-600 rounded-lg">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Project Type -->
            <div class="px-6 py-3 bg-gray-50 border-b border-gray-200">
                <p class="text-sm text-gray-600">Project Type:</p>
            </div>

            <!-- Tabs -->
            <div class="flex border-b border-gray-200">
                <button onclick="switchTab('summary')" id="summaryTab" class="flex-1 px-6 py-3 text-sm font-medium text-orange-600 border-b-2 border-orange-500">
                    Summary View
                </button>
                <button onclick="switchTab('roomwise')" id="roomwiseTab" class="flex-1 px-6 py-3 text-sm font-medium text-gray-500 border-b-2 border-transparent hover:text-gray-700">
                    Room-Wise View
                </button>
            </div>

            <!-- Content -->
            <div class="p-6 overflow-y-auto max-h-96">
                <!-- Summary View -->
                <div id="summaryContent">
                    <div class="overflow-x-auto">
                        <table class="w-full text-sm">
                            <thead>
                                <tr class="border-b border-gray-200">
                                    <th class="text-left py-3 font-medium text-gray-900">SKU</th>
                                    <th class="text-center py-3 font-medium text-gray-900">AREA*UNIT RATE</th>
                                    <th class="text-right py-3 font-medium text-gray-900">TOTAL</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-100">
                                <tr>
                                    <td class="py-4">
                                        <div>
                                            <p class="font-medium text-gray-900">Tractor Emulsion</p>
                                            <p class="text-gray-500">2P</p>
                                        </div>
                                    </td>
                                    <td class="py-4 text-center text-gray-600">1113 sqft x 9.3/sqft</td>
                                    <td class="py-4 text-right font-medium text-gray-900">₹10,351</td>
                                </tr>
                                <tr>
                                    <td class="py-4">
                                        <div>
                                            <p class="font-medium text-gray-900">Royale Luxury Emulsion</p>
                                            <p class="text-gray-500">1Pr + 2P</p>
                                        </div>
                                    </td>
                                    <td class="py-4 text-center text-gray-600">1942 sqft x 20.6/sqft</td>
                                    <td class="py-4 text-right font-medium text-gray-900">₹40,005</td>
                                </tr>
                                <tr>
                                    <td class="py-4">
                                        <div>
                                            <p class="font-medium text-gray-900">POP & Putty Mix</p>
                                            <p class="text-gray-500">Touchups</p>
                                        </div>
                                    </td>
                                    <td class="py-4 text-center text-gray-600">1 sqft x 1000/sqft</td>
                                    <td class="py-4 text-right font-medium text-gray-900">₹1,000</td>
                                </tr>
                                <tr>
                                    <td class="py-4">
                                        <div>
                                            <p class="font-medium text-gray-900">Apcolite Premium Satin Enamel</p>
                                            <p class="text-gray-500">2P</p>
                                        </div>
                                    </td>
                                    <td class="py-4 text-center text-gray-600">350 sqft x 16.5/sqft</td>
                                    <td class="py-4 text-right font-medium text-gray-900">₹5,775</td>
                                </tr>
                                <tr>
                                    <td class="py-4">
                                        <div>
                                            <p class="font-medium text-gray-900">Apex Emulsion</p>
                                            <p class="text-gray-500">2P</p>
                                        </div>
                                    </td>
                                    <td class="py-4 text-center text-gray-600">168 sqft x 15.5/sqft</td>
                                    <td class="py-4 text-right font-medium text-gray-900">₹2,604</td>
                                </tr>
                                <tr>
                                    <td class="py-4">
                                        <div>
                                            <p class="font-medium text-gray-900">Apcolite Premium Satin Enamel</p>
                                            <p class="text-gray-500">2P</p>
                                        </div>
                                    </td>
                                    <td class="py-4 text-center text-gray-600">139 sqft x 16.5/sqft</td>
                                    <td class="py-4 text-right font-medium text-gray-900">₹2,294</td>
                                </tr>
                                <tr>
                                    <td class="py-4">
                                        <div>
                                            <p class="font-medium text-gray-900">Royale Luxury Emulsion</p>
                                            <p class="text-gray-500">1Pr + 2P</p>
                                        </div>
                                    </td>
                                    <td class="py-4 text-center text-gray-600">45 sqft x 20.6/sqft</td>
                                    <td class="py-4 text-right font-medium text-gray-900">₹927</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Room-wise View (Hidden by default) -->
                <div id="roomwiseContent" class="hidden">
                    <div class="space-y-4">
                        <!-- Room List -->
                        <div class="space-y-3">
                            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg border">
                                <span class="font-medium text-gray-900">Kitchen</span>
                                <div class="flex items-center space-x-2">
                                    <span class="font-bold text-gray-900">₹4,971</span>
                                    <svg class="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            </div>

                            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg border">
                                <span class="font-medium text-gray-900">Kids Bedroom</span>
                                <div class="flex items-center space-x-2">
                                    <span class="font-bold text-gray-900">₹5,136</span>
                                    <svg class="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            </div>

                            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg border">
                                <span class="font-medium text-gray-900">Master Bedroom</span>
                                <div class="flex items-center space-x-2">
                                    <span class="font-bold text-gray-900">₹7,929</span>
                                    <svg class="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            </div>

                            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg border">
                                <span class="font-medium text-gray-900">Living Room</span>
                                <div class="flex items-center space-x-2">
                                    <span class="font-bold text-gray-900">₹25,701</span>
                                    <svg class="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            </div>

                            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg border">
                                <span class="font-medium text-gray-900">Other</span>
                                <div class="flex items-center space-x-2">
                                    <span class="font-bold text-gray-900">₹5,471</span>
                                    <svg class="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            </div>

                            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg border">
                                <span class="font-medium text-gray-900">Balcony</span>
                                <div class="flex items-center space-x-2">
                                    <span class="font-bold text-gray-900">₹2,670</span>
                                    <svg class="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            </div>

                            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg border">
                                <span class="font-medium text-gray-900">Balcony</span>
                                <div class="flex items-center space-x-2">
                                    <span class="font-bold text-gray-900">₹3,139</span>
                                    <svg class="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            </div>

                            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg border">
                                <span class="font-medium text-gray-900">Guest Bedroom</span>
                                <div class="flex items-center space-x-2">
                                    <span class="font-bold text-gray-900">₹7,940</span>
                                    <svg class="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>

                        <!-- Payment Milestones Section -->
                        <div class="mt-8 bg-gradient-to-r from-orange-100 to-pink-100 rounded-xl p-6">
                            <h3 class="text-lg font-bold text-gray-900 text-center mb-6">
                                CUSTOMISED PAYMENT MILESTONES FOR YOUR CONVENIENCE
                            </h3>

                            <div class="flex justify-center items-center space-x-8 mb-6">
                                <!-- 1st Pay -->
                                <div class="text-center">
                                    <div class="w-16 h-16 bg-white rounded-full flex items-center justify-center mb-3 mx-auto shadow-sm">
                                        <span class="text-xl font-bold text-gray-900">40%</span>
                                    </div>
                                    <div class="text-sm">
                                        <p class="font-semibold text-gray-900">1st Pay</p>
                                        <p class="text-gray-700">Before Project</p>
                                        <p class="text-gray-700">Start</p>
                                    </div>
                                </div>

                                <!-- 2nd Pay -->
                                <div class="text-center">
                                    <div class="w-16 h-16 bg-white rounded-full flex items-center justify-center mb-3 mx-auto shadow-sm">
                                        <span class="text-xl font-bold text-gray-900">50%</span>
                                    </div>
                                    <div class="text-sm">
                                        <p class="font-semibold text-gray-900">2nd Pay</p>
                                        <p class="text-gray-700">Completion of 50%</p>
                                        <p class="text-gray-700">of the work</p>
                                    </div>
                                </div>

                                <!-- 3rd Pay -->
                                <div class="text-center">
                                    <div class="w-16 h-16 bg-white rounded-full flex items-center justify-center mb-3 mx-auto shadow-sm">
                                        <span class="text-xl font-bold text-gray-900">10%</span>
                                    </div>
                                    <div class="text-sm">
                                        <p class="font-semibold text-gray-900">3rd Pay</p>
                                        <p class="text-gray-700">Upon completion</p>
                                        <p class="text-gray-700">of the work</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Terms and Conditions -->
                        <div class="mt-6 text-center">
                            <p class="text-sm text-gray-600">
                                By accepting the quotation, you are agreeing to the
                                <span class="font-semibold text-gray-900 underline cursor-pointer">TERMS AND CONDITIONS</span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function downloadPDF() {
            alert('PDF download functionality would be implemented here using jsPDF library');
        }

        function payNow() {
            const options = {
                "key": "rzp_test_1234567890", // Replace with your actual Razorpay key
                "amount": 5036400, // Amount in paise (₹50,364)
                "currency": "INR",
                "name": "ShriniPaints",
                "description": "Asian Paints - Painting Project",
                "image": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiByeD0iOCIgZmlsbD0idXJsKCNncmFkaWVudDApIi8+CjxwYXRoIGQ9Ik0xMiAxNkgyOFYyNEgxMlYxNloiIGZpbGw9IndoaXRlIi8+CjxkZWZzPgo8bGluZWFyR3JhZGllbnQgaWQ9ImdyYWRpZW50MCIgeDE9IjAiIHkxPSIwIiB4Mj0iNDAiIHkyPSI0MCI+CjxzdG9wIHN0b3AtY29sb3I9IiNGRjZCNkIiLz4KPHN0b3Agb2Zmc2V0PSIxIiBzdG9wLWNvbG9yPSIjRkZBNTAwIi8+CjwvbGluZWFyR3JhZGllbnQ+CjwvZGVmcz4KPHN2Zz4K",
                "order_id": "order_" + Date.now(), // Generate a unique order ID
                "handler": function (response) {
                    alert("Payment successful! Payment ID: " + response.razorpay_payment_id);
                    console.log("Payment Response:", response);
                    // Here you would typically send the payment details to your server
                },
                "prefill": {
                    "name": "Customer Name",
                    "email": "<EMAIL>",
                    "contact": "**********"
                },
                "notes": {
                    "project_type": "Asian Paints - Residential Painting",
                    "quote_amount": "₹50,364"
                },
                "theme": {
                    "color": "#ff6b35"
                },
                "method": {
                    "netbanking": true,
                    "card": true,
                    "wallet": true,
                    "upi": true,
                    "paylater": true
                }
            };

            const rzp = new Razorpay(options);

            rzp.on('payment.failed', function (response) {
                alert("Payment failed! Error: " + response.error.description);
                console.log("Payment Failed:", response.error);
            });

            rzp.open();
        }

        function toggleTimeline(timelineId) {
            const timeline = document.getElementById(timelineId);
            const arrow = document.getElementById('arrow' + timelineId.slice(-1));

            if (timeline.classList.contains('hidden')) {
                timeline.classList.remove('hidden');
                arrow.classList.add('rotate-180');
            } else {
                timeline.classList.add('hidden');
                arrow.classList.remove('rotate-180');
            }
        }

        function openQuoteModal() {
            document.getElementById('quoteModal').classList.remove('hidden');
        }

        function closeQuoteModal() {
            document.getElementById('quoteModal').classList.add('hidden');
        }

        function switchTab(tab) {
            const summaryTab = document.getElementById('summaryTab');
            const roomwiseTab = document.getElementById('roomwiseTab');
            const summaryContent = document.getElementById('summaryContent');
            const roomwiseContent = document.getElementById('roomwiseContent');

            if (tab === 'summary') {
                summaryTab.classList.add('text-orange-600', 'border-orange-500');
                summaryTab.classList.remove('text-gray-500', 'border-transparent');
                roomwiseTab.classList.add('text-gray-500', 'border-transparent');
                roomwiseTab.classList.remove('text-orange-600', 'border-orange-500');
                summaryContent.classList.remove('hidden');
                roomwiseContent.classList.add('hidden');
            } else {
                roomwiseTab.classList.add('text-orange-600', 'border-orange-500');
                roomwiseTab.classList.remove('text-gray-500', 'border-transparent');
                summaryTab.classList.add('text-gray-500', 'border-transparent');
                summaryTab.classList.remove('text-orange-600', 'border-orange-500');
                roomwiseContent.classList.remove('hidden');
                summaryContent.classList.add('hidden');
            }
        }

        function downloadQuotePDF() {
            // Create a comprehensive PDF content
            const pdfContent = `
QUOTATION - ASIAN PAINTS
Project Type: Residential Painting
Total Amount: ₹50,364

SUMMARY VIEW:
1. Tractor Emulsion (2P) - 1113 sqft x 9.3/sqft = ₹10,351
2. Royale Luxury Emulsion (1Pr + 2P) - 1942 sqft x 20.6/sqft = ₹40,005
3. POP & Putty Mix (Touchups) - 1 sqft x 1000/sqft = ₹1,000
4. Apcolite Premium Satin Enamel (2P) - 350 sqft x 16.5/sqft = ₹5,775
5. Apex Emulsion (2P) - 168 sqft x 15.5/sqft = ₹2,604
6. Apcolite Premium Satin Enamel (2P) - 139 sqft x 16.5/sqft = ₹2,294
7. Royale Luxury Emulsion (1Pr + 2P) - 45 sqft x 20.6/sqft = ₹927

ROOM-WISE BREAKDOWN:
• Kitchen - ₹4,971
• Kids Bedroom - ₹5,136
• Master Bedroom - ₹7,929
• Living Room - ₹25,701
• Other - ₹5,471
• Balcony - ₹2,670
• Balcony - ₹3,139
• Guest Bedroom - ₹7,940

PAYMENT MILESTONES:
• 1st Pay (40%) - Before Project Start
• 2nd Pay (50%) - Completion of 50% of the work
• 3rd Pay (10%) - Upon completion of the work

TOTAL: ₹50,364

Generated by ShriniPaints
Date: ${new Date().toLocaleDateString()}

By downloading this quotation, you agree to the Terms and Conditions.
            `;

            // Create and download the file
            const blob = new Blob([pdfContent], { type: 'text/plain' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'ShriniPaints_Quotation_Asian_Paints.txt';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            alert('Quotation downloaded successfully!');
        }

        // Chat functionality
        function toggleChatWindow() {
            const chatWindow = document.getElementById('chatWindow');
            if (chatWindow.style.display === 'flex') {
                chatWindow.style.display = 'none';
            } else {
                chatWindow.style.display = 'flex';
                // Auto-focus on input when opening
                setTimeout(() => {
                    document.getElementById('chatInput').focus();
                }, 100);
            }
        }

        function closeChatWindow() {
            document.getElementById('chatWindow').style.display = 'none';
        }

        function handleChatKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        function sendMessage() {
            const input = document.getElementById('chatInput');
            const message = input.value.trim();

            if (message === '') return;

            // Add user message
            addMessage(message, 'user');
            input.value = '';

            // Show typing indicator
            showTypingIndicator();

            // Simulate bot response after delay
            setTimeout(() => {
                hideTypingIndicator();
                respondToMessage(message);
            }, 1000 + Math.random() * 1000);
        }

        function addMessage(text, sender) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            messageDiv.textContent = text;
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function showTypingIndicator() {
            const messagesContainer = document.getElementById('chatMessages');
            const typingDiv = document.createElement('div');
            typingDiv.className = 'typing-indicator';
            typingDiv.id = 'typingIndicator';
            typingDiv.innerHTML = '<div class="typing-dot"></div><div class="typing-dot"></div><div class="typing-dot"></div>';
            messagesContainer.appendChild(typingDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function hideTypingIndicator() {
            const typingIndicator = document.getElementById('typingIndicator');
            if (typingIndicator) {
                typingIndicator.remove();
            }
        }

        function respondToMessage(userMessage) {
            const lowerMessage = userMessage.toLowerCase();
            let response = '';

            if (lowerMessage.includes('price') || lowerMessage.includes('cost') || lowerMessage.includes('quote')) {
                response = "Our current quote for Asian Paints is ₹50,364 with a 20% discount! This includes premium quality paints and professional application. Would you like to see the detailed breakdown?";
            } else if (lowerMessage.includes('payment') || lowerMessage.includes('pay')) {
                response = "We offer flexible payment options:\n• 40% before project start\n• 50% at 50% completion\n• 10% upon completion\n\nWe accept all payment methods through Razorpay - cards, UPI, net banking, and wallets!";
            } else if (lowerMessage.includes('time') || lowerMessage.includes('duration') || lowerMessage.includes('when')) {
                response = "Your project is scheduled to start on Wednesday, Jul-16-2025. Typical painting projects take 5-7 days depending on the scope. We'll keep you updated throughout!";
            } else if (lowerMessage.includes('contact') || lowerMessage.includes('phone') || lowerMessage.includes('call')) {
                response = "You can reach me directly:\n📞 Phone: +91-XXXXXXXXXX\n📧 Email: <EMAIL>\n\nI'm Shrinivas, your dedicated project manager. Feel free to call anytime!";
            } else if (lowerMessage.includes('hello') || lowerMessage.includes('hi') || lowerMessage.includes('hey')) {
                response = "Hello! Great to hear from you! 😊 I'm here to help with any questions about your painting project. What would you like to know?";
            } else if (lowerMessage.includes('thank') || lowerMessage.includes('thanks')) {
                response = "You're very welcome! I'm always here to help. Don't hesitate to reach out if you have any other questions about your project! 😊";
            } else {
                response = "Thanks for your message! For specific queries, you can contact me directly at:\n📞 +91-XXXXXXXXXX\n📧 <EMAIL>\n\nI'm Shrinivas from ShriniPaints, and I'm here to ensure your painting project goes smoothly!";
            }

            addMessage(response, 'bot');
        }

        // Section management
        function showHomeSection() {
            hideAllSections();
            document.getElementById('homeSection').style.display = 'block';
            updateActiveNavItem('home');
        }

        function showColoursSection() {
            hideAllSections();
            // Create colours section if it doesn't exist
            if (!document.getElementById('coloursSection')) {
                createColoursSection();
            }
            document.getElementById('coloursSection').style.display = 'block';
            updateActiveNavItem('colours');
        }

        function showPaymentsSection() {
            hideAllSections();
            // Create payments section if it doesn't exist
            if (!document.getElementById('paymentsSection')) {
                createPaymentsSection();
            }
            document.getElementById('paymentsSection').style.display = 'block';
            updateActiveNavItem('payments');
        }

        function hideAllSections() {
            const sections = ['homeSection', 'coloursSection', 'paymentsSection'];
            sections.forEach(sectionId => {
                const section = document.getElementById(sectionId);
                if (section) {
                    section.style.display = 'none';
                }
            });
        }

        function updateActiveNavItem(activeItem) {
            // Remove active class from all nav items
            const navItems = document.querySelectorAll('nav a');
            navItems.forEach(item => {
                item.classList.remove('text-white', 'bg-orange-500');
                item.classList.add('text-gray-600', 'hover:text-gray-900', 'hover:bg-gray-50');
            });

            // Add active class to current item
            const activeNavItem = document.querySelector(`nav a[onclick*="${activeItem}"]`);
            if (activeNavItem) {
                activeNavItem.classList.remove('text-gray-600', 'hover:text-gray-900', 'hover:bg-gray-50');
                activeNavItem.classList.add('text-white', 'bg-orange-500');
            }
        }

        function createColoursSection() {
            const main = document.querySelector('main');
            const coloursSection = document.createElement('div');
            coloursSection.id = 'coloursSection';
            coloursSection.style.display = 'none';
            coloursSection.innerHTML = `
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Color Selection</h2>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div class="text-center p-4 border rounded-lg hover:shadow-md transition-shadow">
                            <div class="w-16 h-16 bg-blue-500 rounded-full mx-auto mb-2"></div>
                            <p class="font-medium">Royal Blue</p>
                        </div>
                        <div class="text-center p-4 border rounded-lg hover:shadow-md transition-shadow">
                            <div class="w-16 h-16 bg-green-500 rounded-full mx-auto mb-2"></div>
                            <p class="font-medium">Forest Green</p>
                        </div>
                        <div class="text-center p-4 border rounded-lg hover:shadow-md transition-shadow">
                            <div class="w-16 h-16 bg-yellow-500 rounded-full mx-auto mb-2"></div>
                            <p class="font-medium">Sunshine Yellow</p>
                        </div>
                        <div class="text-center p-4 border rounded-lg hover:shadow-md transition-shadow">
                            <div class="w-16 h-16 bg-purple-500 rounded-full mx-auto mb-2"></div>
                            <p class="font-medium">Royal Purple</p>
                        </div>
                    </div>
                </div>
            `;
            main.appendChild(coloursSection);
        }

        function createPaymentsSection() {
            const main = document.querySelector('main');
            const paymentsSection = document.createElement('div');
            paymentsSection.id = 'paymentsSection';
            paymentsSection.style.display = 'none';
            paymentsSection.innerHTML = `
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-6">Payment Options</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="border rounded-lg p-6 hover:shadow-md transition-shadow">
                            <h3 class="text-lg font-semibold mb-4">Quick Payment</h3>
                            <p class="text-gray-600 mb-4">Pay the full amount now and get additional discount</p>
                            <button onclick="payNow()" class="w-full bg-green-600 hover:bg-green-700 text-white py-3 rounded-lg font-medium transition-colors">
                                Pay ₹50,364 Now
                            </button>
                        </div>
                        <div class="border rounded-lg p-6 hover:shadow-md transition-shadow">
                            <h3 class="text-lg font-semibold mb-4">Milestone Payments</h3>
                            <div class="space-y-3 mb-4">
                                <div class="flex justify-between">
                                    <span>1st Payment (40%)</span>
                                    <span class="font-medium">₹20,146</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>2nd Payment (50%)</span>
                                    <span class="font-medium">₹25,182</span>
                                </div>
                                <div class="flex justify-between">
                                    <span>Final Payment (10%)</span>
                                    <span class="font-medium">₹5,036</span>
                                </div>
                            </div>
                            <button onclick="payNow()" class="w-full bg-orange-500 hover:bg-orange-600 text-white py-3 rounded-lg font-medium transition-colors">
                                Pay 1st Milestone
                            </button>
                        </div>
                    </div>
                </div>
            `;
            main.appendChild(paymentsSection);
        }

        // Add some interactivity
        document.addEventListener('DOMContentLoaded', function() {
            console.log('ShriniPaints Demo Loaded Successfully!');

            // Auto-expand first timeline item
            toggleTimeline('timeline1');

            // Show home section by default
            showHomeSection();
        });
    </script>
</body>
</html>
