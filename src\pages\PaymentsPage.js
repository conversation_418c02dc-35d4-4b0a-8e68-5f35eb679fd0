import React, { useState } from 'react';
import { CreditCard, Calendar, CheckCircle, Clock, AlertCircle, Download, ExternalLink } from 'lucide-react';
import { projectData, paymentSchedule } from '../data/dummyData';

const PaymentsPage = () => {
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [selectedPayment, setSelectedPayment] = useState(null);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  const getPaymentStatusIcon = (status) => {
    switch (status) {
      case 'paid':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'due':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      case 'pending':
        return <Clock className="w-5 h-5 text-yellow-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-400" />;
    }
  };

  const getPaymentStatusColor = (status) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'due':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const handlePayNow = (payment) => {
    setSelectedPayment(payment);
    setShowPaymentModal(true);
    // Simulate payment processing
    setTimeout(() => {
      setShowPaymentModal(false);
      alert('Payment gateway integration would be implemented here');
    }, 2000);
  };

  const completionPercentage = Math.round((projectData.paidAmount / projectData.totalAmount) * 100);
  const paidPayments = paymentSchedule.filter(p => p.status === 'paid');
  const duePayments = paymentSchedule.filter(p => p.status === 'due');
  const pendingPayments = paymentSchedule.filter(p => p.status === 'pending');

  return (
    <>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 space-y-6">
        {/* Payment Overview Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="card bg-gradient-to-r from-primary-50 to-primary-100 border-primary-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-primary-700">Total Amount</p>
                <p className="text-2xl font-bold text-primary-900">{formatCurrency(projectData.totalAmount)}</p>
              </div>
              <CreditCard className="w-8 h-8 text-primary-600" />
            </div>
          </div>
          
          <div className="card bg-gradient-to-r from-green-50 to-green-100 border-green-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-700">Paid Amount</p>
                <p className="text-2xl font-bold text-green-900">{formatCurrency(projectData.paidAmount)}</p>
                <p className="text-xs text-green-600 mt-1">{completionPercentage}% completed</p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-600" />
            </div>
          </div>
          
          <div className="card bg-gradient-to-r from-red-50 to-red-100 border-red-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-red-700">Due Amount</p>
                <p className="text-2xl font-bold text-red-900">{formatCurrency(projectData.dueAmount)}</p>
                <p className="text-xs text-red-600 mt-1">{duePayments.length} payment(s) due</p>
              </div>
              <AlertCircle className="w-8 h-8 text-red-600" />
            </div>
          </div>
        </div>

        {/* Payment Progress */}
        <div className="card">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Payment Progress</h2>
          <div className="mb-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">Overall Progress</span>
              <span className="text-sm text-gray-600">{completionPercentage}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-4">
              <div 
                className="bg-gradient-to-r from-green-500 to-green-600 h-4 rounded-full transition-all duration-500"
                style={{ width: `${completionPercentage}%` }}
              ></div>
            </div>
          </div>
          
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <p className="text-2xl font-bold text-green-600">{paidPayments.length}</p>
              <p className="text-sm text-gray-600">Paid</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-red-600">{duePayments.length}</p>
              <p className="text-sm text-gray-600">Due</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-yellow-600">{pendingPayments.length}</p>
              <p className="text-sm text-gray-600">Pending</p>
            </div>
          </div>
        </div>

        {/* Payment Schedule */}
        <div className="card">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-gray-900">Payment Schedule</h2>
            <button className="flex items-center space-x-2 text-primary-600 hover:text-primary-700 text-sm font-medium">
              <Download className="w-4 h-4" />
              <span>Download Receipt</span>
            </button>
          </div>
          
          <div className="space-y-4">
            {paymentSchedule.map((payment) => (
              <div key={payment.id} className={`border rounded-lg p-4 ${getPaymentStatusColor(payment.status)}`}>
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-3">
                    {getPaymentStatusIcon(payment.status)}
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900 mb-1">{payment.title}</h3>
                      <p className="text-sm text-gray-600 mb-2">{payment.description}</p>
                      <div className="flex items-center space-x-4 text-xs text-gray-500">
                        <span className="flex items-center space-x-1">
                          <Calendar className="w-3 h-3" />
                          <span>
                            {payment.status === 'paid' ? `Paid: ${formatDate(payment.paidDate)}` : `Due: ${formatDate(payment.dueDate)}`}
                          </span>
                        </span>
                        <span>{payment.percentage}% of total</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <p className="text-lg font-bold text-gray-900 mb-2">
                      {formatCurrency(payment.amount)}
                    </p>
                    
                    {payment.status === 'due' && (
                      <button
                        onClick={() => handlePayNow(payment)}
                        className="inline-flex items-center space-x-2 bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm font-medium transition-colors duration-200"
                      >
                        <CreditCard className="w-3 h-3" />
                        <span>Pay Now</span>
                        <ExternalLink className="w-3 h-3" />
                      </button>
                    )}
                    
                    {payment.status === 'paid' && (
                      <span className="inline-flex items-center space-x-1 text-green-600 text-sm">
                        <CheckCircle className="w-3 h-3" />
                        <span>Paid</span>
                      </span>
                    )}
                    
                    {payment.status === 'pending' && (
                      <span className="inline-flex items-center space-x-1 text-yellow-600 text-sm">
                        <Clock className="w-3 h-3" />
                        <span>Pending</span>
                      </span>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Payment Methods */}
        <div className="card">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Accepted Payment Methods</h2>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {['UPI', 'Credit Card', 'Debit Card', 'Net Banking'].map((method) => (
              <div key={method} className="flex items-center justify-center p-3 bg-gray-50 rounded-lg border">
                <span className="text-sm font-medium text-gray-700">{method}</span>
              </div>
            ))}
          </div>
          <p className="text-xs text-gray-500 mt-4 text-center">
            All payments are processed securely through our payment gateway partners
          </p>
        </div>
      </div>

      {/* Payment Processing Modal */}
      {showPaymentModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 max-w-sm w-full">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Processing Payment</h3>
              <p className="text-sm text-gray-600 mb-4">
                {selectedPayment && `Processing ${formatCurrency(selectedPayment.amount)} payment...`}
              </p>
              <p className="text-xs text-gray-500">Redirecting to secure payment gateway...</p>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default PaymentsPage;
