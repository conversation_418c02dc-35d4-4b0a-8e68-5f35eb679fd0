import React from 'react';
import { FileText, Download, Phone, MessageCircle, Calendar, Camera } from 'lucide-react';
import { generateQuotationPDF } from '../utils/pdfGenerator';

const QuickActions = ({ onQuoteClick }) => {
  const actions = [
    {
      id: 1,
      title: 'View Quote',
      description: 'See detailed quotation',
      icon: FileText,
      color: 'bg-blue-500 hover:bg-blue-600',
      onClick: onQuoteClick
    },
    {
      id: 2,
      title: 'Download PDF',
      description: 'Get quotation PDF',
      icon: Download,
      color: 'bg-green-500 hover:bg-green-600',
      onClick: async () => {
        try {
          await generateQuotationPDF();
        } catch (error) {
          console.error('Error generating PDF:', error);
          alert('Error generating PDF. Please try again.');
        }
      }
    },
    {
      id: 3,
      title: 'Call Support',
      description: 'Contact project manager',
      icon: Phone,
      color: 'bg-purple-500 hover:bg-purple-600',
      onClick: () => {
        window.location.href = 'tel:+919876543210';
      }
    },
    {
      id: 4,
      title: 'Send Message',
      description: 'WhatsApp support',
      icon: MessageCircle,
      color: 'bg-green-600 hover:bg-green-700',
      onClick: () => {
        window.open('https://wa.me/919876543210?text=Hi, I need help with my painting project', '_blank');
      }
    },
    {
      id: 5,
      title: 'Schedule Visit',
      description: 'Book inspection',
      icon: Calendar,
      color: 'bg-orange-500 hover:bg-orange-600',
      onClick: () => {
        alert('Schedule visit feature will be implemented');
      }
    },
    {
      id: 6,
      title: 'Progress Photos',
      description: 'View work photos',
      icon: Camera,
      color: 'bg-pink-500 hover:bg-pink-600',
      onClick: () => {
        alert('Progress photos feature will be implemented');
      }
    }
  ];

  return (
    <div className="card">
      <h2 className="text-xl font-semibold text-gray-900 mb-6">Quick Actions</h2>
      
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
        {actions.map((action) => {
          const Icon = action.icon;
          return (
            <button
              key={action.id}
              onClick={action.onClick}
              className="flex flex-col items-center p-4 rounded-lg border border-gray-200 hover:border-gray-300 transition-all duration-200 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
            >
              <div className={`w-12 h-12 rounded-full ${action.color} flex items-center justify-center mb-3 transition-colors duration-200`}>
                <Icon className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-sm font-medium text-gray-900 text-center mb-1">
                {action.title}
              </h3>
              <p className="text-xs text-gray-600 text-center">
                {action.description}
              </p>
            </button>
          );
        })}
      </div>
    </div>
  );
};

export default QuickActions;
