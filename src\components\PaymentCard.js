import React, { useState } from 'react';
import { CreditCard, Calendar, AlertCircle, CheckCircle, Clock, ExternalLink } from 'lucide-react';
import { projectData, paymentSchedule } from '../data/dummyData';

const PaymentCard = () => {
  const [showPaymentModal, setShowPaymentModal] = useState(false);

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  const getPaymentStatusIcon = (status) => {
    switch (status) {
      case 'paid':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'due':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const getPaymentStatusColor = (status) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'due':
        return 'bg-red-100 text-red-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const nextDuePayment = paymentSchedule.find(payment => payment.status === 'due');
  const completionPercentage = Math.round((projectData.paidAmount / projectData.totalAmount) * 100);

  const handlePayNow = () => {
    setShowPaymentModal(true);
    // In a real app, this would integrate with a payment gateway
    setTimeout(() => {
      setShowPaymentModal(false);
      alert('Payment gateway integration would be implemented here');
    }, 2000);
  };

  return (
    <>
      <div className="card">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900">Payment Overview</h2>
          <CreditCard className="w-6 h-6 text-primary-600" />
        </div>

        {/* Payment Summary */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-gradient-to-r from-primary-50 to-primary-100 rounded-lg p-4">
            <p className="text-sm font-medium text-primary-700 mb-1">Total Amount</p>
            <p className="text-2xl font-bold text-primary-900">{formatCurrency(projectData.totalAmount)}</p>
          </div>
          
          <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-4">
            <p className="text-sm font-medium text-green-700 mb-1">Paid Amount</p>
            <p className="text-2xl font-bold text-green-900">{formatCurrency(projectData.paidAmount)}</p>
            <p className="text-xs text-green-600 mt-1">{completionPercentage}% completed</p>
          </div>
          
          <div className="bg-gradient-to-r from-red-50 to-red-100 rounded-lg p-4">
            <p className="text-sm font-medium text-red-700 mb-1">Due Amount</p>
            <p className="text-2xl font-bold text-red-900">{formatCurrency(projectData.dueAmount)}</p>
            {nextDuePayment && (
              <p className="text-xs text-red-600 mt-1">Due: {formatDate(nextDuePayment.dueDate)}</p>
            )}
          </div>
        </div>

        {/* Payment Progress */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">Payment Progress</span>
            <span className="text-sm text-gray-600">{completionPercentage}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div 
              className="bg-gradient-to-r from-green-500 to-green-600 h-3 rounded-full transition-all duration-500"
              style={{ width: `${completionPercentage}%` }}
            ></div>
          </div>
        </div>

        {/* Next Payment Due */}
        {nextDuePayment && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="flex items-start space-x-3">
              <AlertCircle className="w-5 h-5 text-red-500 mt-0.5" />
              <div className="flex-1">
                <h3 className="text-sm font-medium text-red-800 mb-1">Payment Due</h3>
                <p className="text-sm text-red-700 mb-2">
                  {nextDuePayment.title} - {formatCurrency(nextDuePayment.amount)} ({nextDuePayment.percentage}%)
                </p>
                <p className="text-xs text-red-600 mb-3">
                  Due Date: {formatDate(nextDuePayment.dueDate)}
                </p>
                <button
                  onClick={handlePayNow}
                  className="inline-flex items-center space-x-2 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                >
                  <CreditCard className="w-4 h-4" />
                  <span>Pay Now</span>
                  <ExternalLink className="w-3 h-3" />
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Payment Schedule */}
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-4">Payment Schedule</h3>
          <div className="space-y-3">
            {paymentSchedule.map((payment) => (
              <div key={payment.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  {getPaymentStatusIcon(payment.status)}
                  <div>
                    <p className="text-sm font-medium text-gray-900">{payment.title}</p>
                    <p className="text-xs text-gray-600">{payment.description}</p>
                  </div>
                </div>
                
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-900">
                    {formatCurrency(payment.amount)}
                  </p>
                  <div className="flex items-center space-x-2">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPaymentStatusColor(payment.status)}`}>
                      {payment.status === 'paid' ? 'Paid' : payment.status === 'due' ? 'Due' : 'Pending'}
                    </span>
                    <span className="text-xs text-gray-500">
                      {payment.status === 'paid' ? formatDate(payment.paidDate) : formatDate(payment.dueDate)}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Payment Modal */}
      {showPaymentModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 max-w-sm w-full">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">Processing Payment</h3>
              <p className="text-sm text-gray-600">Redirecting to secure payment gateway...</p>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default PaymentCard;
