import React, { useState } from 'react';
import { 
  Phone, 
  Mail, 
  MessageCircle, 
  Clock, 
  HelpCircle, 
  Send, 
  User,
  Calendar,
  Camera,
  FileText,
  Star
} from 'lucide-react';
import { supportContacts, projectData } from '../data/dummyData';

const SupportPage = () => {
  const [activeTab, setActiveTab] = useState('contact');
  const [message, setMessage] = useState('');
  const [rating, setRating] = useState(0);
  const [feedback, setFeedback] = useState('');

  const handleCall = (phone) => {
    window.location.href = `tel:${phone}`;
  };

  const handleEmail = (email) => {
    window.location.href = `mailto:${email}?subject=Support Request - ${projectData.id}`;
  };

  const handleWhatsApp = (phone) => {
    const message = `Hi, I need help with my painting project ${projectData.id}`;
    window.open(`https://wa.me/${phone.replace(/[^0-9]/g, '')}?text=${encodeURIComponent(message)}`, '_blank');
  };

  const handleSendMessage = () => {
    if (message.trim()) {
      alert('Message sent successfully! Our team will get back to you soon.');
      setMessage('');
    }
  };

  const handleSubmitFeedback = () => {
    if (rating > 0 && feedback.trim()) {
      alert('Thank you for your feedback! We appreciate your input.');
      setRating(0);
      setFeedback('');
    }
  };

  const tabs = [
    { id: 'contact', label: 'Contact', icon: Phone },
    { id: 'message', label: 'Message', icon: MessageCircle },
    { id: 'faq', label: 'FAQ', icon: HelpCircle },
    { id: 'feedback', label: 'Feedback', icon: Star }
  ];

  const faqs = [
    {
      question: "How can I track my project progress?",
      answer: "You can track your project progress on the Home page where you'll find a detailed timeline showing all completed and upcoming milestones."
    },
    {
      question: "When is my next payment due?",
      answer: "Check the Payments page for your complete payment schedule. You'll receive notifications before each payment due date."
    },
    {
      question: "Can I change paint colors after approval?",
      answer: "Color changes are possible but may affect the timeline and cost. Please contact your project manager to discuss any changes."
    },
    {
      question: "What if I'm not satisfied with the work?",
      answer: "We offer a 1-year warranty on workmanship. If you're not satisfied, contact us immediately and we'll address your concerns."
    },
    {
      question: "How do I schedule a site visit?",
      answer: "You can schedule a site visit by calling your project manager or using the 'Schedule Visit' option in Quick Actions on the Home page."
    }
  ];

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      {/* Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">Support Center</h1>
        <p className="text-gray-600">Get help with your painting project</p>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-gray-100 rounded-lg p-1 mb-6 overflow-x-auto">
        {tabs.map((tab) => {
          const Icon = tab.icon;
          return (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 whitespace-nowrap ${
                activeTab === tab.id
                  ? 'bg-white text-primary-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <Icon className="w-4 h-4" />
              <span>{tab.label}</span>
            </button>
          );
        })}
      </div>

      {/* Contact Tab */}
      {activeTab === 'contact' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {supportContacts.map((contact) => (
              <div key={contact.id} className="card">
                <div className="text-center mb-4">
                  <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <User className="w-8 h-8 text-primary-600" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900">{contact.name}</h3>
                  <p className="text-sm text-gray-600">{contact.role}</p>
                </div>
                
                <div className="space-y-3">
                  <button
                    onClick={() => handleCall(contact.phone)}
                    className="w-full flex items-center justify-center space-x-2 bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg transition-colors duration-200"
                  >
                    <Phone className="w-4 h-4" />
                    <span>Call Now</span>
                  </button>
                  
                  <button
                    onClick={() => handleWhatsApp(contact.phone)}
                    className="w-full flex items-center justify-center space-x-2 bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-lg transition-colors duration-200"
                  >
                    <MessageCircle className="w-4 h-4" />
                    <span>WhatsApp</span>
                  </button>
                  
                  <button
                    onClick={() => handleEmail(contact.email)}
                    className="w-full flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg transition-colors duration-200"
                  >
                    <Mail className="w-4 h-4" />
                    <span>Email</span>
                  </button>
                </div>
                
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <Clock className="w-4 h-4" />
                    <span>{contact.available}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Quick Actions */}
          <div className="card">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <button className="flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                <Calendar className="w-8 h-8 text-blue-600 mb-2" />
                <span className="text-sm font-medium text-gray-900">Schedule Visit</span>
              </button>
              <button className="flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                <Camera className="w-8 h-8 text-green-600 mb-2" />
                <span className="text-sm font-medium text-gray-900">Progress Photos</span>
              </button>
              <button className="flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                <FileText className="w-8 h-8 text-purple-600 mb-2" />
                <span className="text-sm font-medium text-gray-900">Documents</span>
              </button>
              <button className="flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                <HelpCircle className="w-8 h-8 text-orange-600 mb-2" />
                <span className="text-sm font-medium text-gray-900">Report Issue</span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Message Tab */}
      {activeTab === 'message' && (
        <div className="card max-w-2xl mx-auto">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Send Message</h2>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Project ID
              </label>
              <input
                type="text"
                value={projectData.id}
                disabled
                className="input-field bg-gray-50"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Message
              </label>
              <textarea
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                placeholder="Describe your query or concern..."
                rows={6}
                className="input-field resize-none"
              />
            </div>
            
            <button
              onClick={handleSendMessage}
              disabled={!message.trim()}
              className="w-full flex items-center justify-center space-x-2 bg-primary-600 hover:bg-primary-700 disabled:bg-gray-300 disabled:cursor-not-allowed text-white py-3 px-4 rounded-lg transition-colors duration-200"
            >
              <Send className="w-4 h-4" />
              <span>Send Message</span>
            </button>
          </div>
        </div>
      )}

      {/* FAQ Tab */}
      {activeTab === 'faq' && (
        <div className="space-y-4">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Frequently Asked Questions</h2>
          {faqs.map((faq, index) => (
            <div key={index} className="card">
              <h3 className="text-lg font-medium text-gray-900 mb-3">{faq.question}</h3>
              <p className="text-gray-600">{faq.answer}</p>
            </div>
          ))}
        </div>
      )}

      {/* Feedback Tab */}
      {activeTab === 'feedback' && (
        <div className="card max-w-2xl mx-auto">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Share Your Feedback</h2>
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Rate your experience
              </label>
              <div className="flex space-x-2">
                {[1, 2, 3, 4, 5].map((star) => (
                  <button
                    key={star}
                    onClick={() => setRating(star)}
                    className={`w-8 h-8 ${
                      star <= rating ? 'text-yellow-400' : 'text-gray-300'
                    } hover:text-yellow-400 transition-colors duration-200`}
                  >
                    <Star className="w-full h-full fill-current" />
                  </button>
                ))}
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Your feedback
              </label>
              <textarea
                value={feedback}
                onChange={(e) => setFeedback(e.target.value)}
                placeholder="Tell us about your experience..."
                rows={6}
                className="input-field resize-none"
              />
            </div>
            
            <button
              onClick={handleSubmitFeedback}
              disabled={rating === 0 || !feedback.trim()}
              className="w-full flex items-center justify-center space-x-2 bg-primary-600 hover:bg-primary-700 disabled:bg-gray-300 disabled:cursor-not-allowed text-white py-3 px-4 rounded-lg transition-colors duration-200"
            >
              <Send className="w-4 h-4" />
              <span>Submit Feedback</span>
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default SupportPage;
