import React from 'react';
import { Calendar, MapPin, Clock, User } from 'lucide-react';
import { projectData } from '../data/dummyData';

const ProjectOverview = () => {
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-IN', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'in_progress':
        return 'In Progress';
      case 'completed':
        return 'Completed';
      case 'pending':
        return 'Pending';
      default:
        return 'Unknown';
    }
  };

  const calculateDaysRemaining = () => {
    const today = new Date();
    const endDate = new Date(projectData.expectedEndDate);
    const diffTime = endDate - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const daysRemaining = calculateDaysRemaining();

  return (
    <div className="card">
      <div className="flex items-start justify-between mb-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            {projectData.projectTitle}
          </h1>
          <p className="text-gray-600 text-sm">
            Project ID: {projectData.id}
          </p>
        </div>
        <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(projectData.status)}`}>
          {getStatusText(projectData.status)}
        </span>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        {/* Customer Info */}
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            <User className="w-5 h-5 text-gray-400" />
          </div>
          <div>
            <p className="text-sm font-medium text-gray-900">Customer</p>
            <p className="text-sm text-gray-600">{projectData.customerName}</p>
          </div>
        </div>

        {/* Location */}
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0 mt-0.5">
            <MapPin className="w-5 h-5 text-gray-400" />
          </div>
          <div>
            <p className="text-sm font-medium text-gray-900">Location</p>
            <p className="text-sm text-gray-600">{projectData.address}</p>
          </div>
        </div>

        {/* Start Date */}
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            <Calendar className="w-5 h-5 text-gray-400" />
          </div>
          <div>
            <p className="text-sm font-medium text-gray-900">Start Date</p>
            <p className="text-sm text-gray-600">{formatDate(projectData.startDate)}</p>
          </div>
        </div>

        {/* Expected End Date */}
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            <Clock className="w-5 h-5 text-gray-400" />
          </div>
          <div>
            <p className="text-sm font-medium text-gray-900">Expected Completion</p>
            <p className="text-sm text-gray-600">
              {formatDate(projectData.expectedEndDate)}
              {daysRemaining > 0 && (
                <span className="ml-2 text-xs text-blue-600">
                  ({daysRemaining} days remaining)
                </span>
              )}
              {daysRemaining < 0 && (
                <span className="ml-2 text-xs text-red-600">
                  ({Math.abs(daysRemaining)} days overdue)
                </span>
              )}
              {daysRemaining === 0 && (
                <span className="ml-2 text-xs text-green-600">
                  (Due today)
                </span>
              )}
            </p>
          </div>
        </div>
      </div>

      {/* Progress Bar */}
      <div className="bg-gray-50 rounded-lg p-4">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-gray-700">Project Progress</span>
          <span className="text-sm text-gray-600">60% Complete</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-gradient-to-r from-primary-500 to-primary-600 h-2 rounded-full transition-all duration-500"
            style={{ width: '60%' }}
          ></div>
        </div>
        <p className="text-xs text-gray-500 mt-2">
          Wall preparation completed, first coat in progress
        </p>
      </div>
    </div>
  );
};

export default ProjectOverview;
