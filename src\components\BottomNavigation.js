import React from 'react';
import { NavLink } from 'react-router-dom';
import { Home, Palette, CreditCard, HelpCircle } from 'lucide-react';

const BottomNavigation = () => {
  const navItems = [
    {
      path: '/',
      icon: Home,
      label: 'Home',
      exact: true
    },
    {
      path: '/colours',
      icon: Palette,
      label: 'Colours'
    },
    {
      path: '/payments',
      icon: CreditCard,
      label: 'Payments'
    },
    {
      path: '/support',
      icon: HelpCircle,
      label: 'Support'
    }
  ];

  return (
    <nav className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 md:hidden safe-area-bottom z-50">
      <div className="flex justify-around items-center h-16">
        {navItems.map((item) => {
          const Icon = item.icon;
          return (
            <NavLink
              key={item.path}
              to={item.path}
              end={item.exact}
              className={({ isActive }) =>
                `bottom-nav-item ${isActive ? 'active' : ''}`
              }
            >
              <Icon className="w-5 h-5 mb-1" />
              <span>{item.label}</span>
            </NavLink>
          );
        })}
      </div>
    </nav>
  );
};

export default BottomNavigation;
