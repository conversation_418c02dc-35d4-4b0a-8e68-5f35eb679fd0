import React, { useState } from 'react';
import { Palette, Search, Filter, Eye } from 'lucide-react';
import { colorPalette, quotationData } from '../data/dummyData';

const ColoursPage = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedColor, setSelectedColor] = useState(null);

  const categories = ['all', 'Neutrals', 'Blues', 'Greens', 'Yellows', 'Whites'];

  const filteredColors = colorPalette.filter(color => {
    const matchesSearch = color.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         color.room.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || color.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const getColorValue = (colorName) => {
    const colorMap = {
      'Warm White': '#F8F6F0',
      'Light Blue': '#E3F2FD',
      'Soft Green': '#E8F5E8',
      'Cream': '#FFF8DC',
      'Light Yellow': '#FFFACD',
      'Pure White': '#FFFFFF'
    };
    return colorMap[colorName] || '#FFFFFF';
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      {/* Header */}
      <div className="mb-6">
        <div className="flex items-center space-x-3 mb-4">
          <Palette className="w-8 h-8 text-primary-600" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Color Palette</h1>
            <p className="text-gray-600">Selected colors for your project</p>
          </div>
        </div>

        {/* Search and Filter */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search colors or rooms..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="input-field pl-10"
            />
          </div>
          
          <div className="relative">
            <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="input-field pl-10 pr-8 appearance-none bg-white"
            >
              {categories.map(category => (
                <option key={category} value={category}>
                  {category === 'all' ? 'All Categories' : category}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Color Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {filteredColors.map((color) => (
          <div
            key={color.id}
            className="card cursor-pointer transition-all duration-200 hover:scale-105"
            onClick={() => setSelectedColor(color)}
          >
            <div className="flex items-center space-x-4">
              <div
                className="w-16 h-16 rounded-lg border-2 border-gray-200 shadow-sm"
                style={{ backgroundColor: getColorValue(color.name) }}
              ></div>
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-gray-900">{color.name}</h3>
                <p className="text-sm text-gray-600">{color.category}</p>
                <p className="text-sm text-primary-600 font-medium">{color.room}</p>
              </div>
              <Eye className="w-5 h-5 text-gray-400" />
            </div>
          </div>
        ))}
      </div>

      {/* Room-wise Color Assignment */}
      <div className="card">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">Room-wise Color Assignment</h2>
        <div className="space-y-4">
          {quotationData.rooms.map((room) => (
            <div key={room.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-4">
                <div
                  className="w-8 h-8 rounded border-2 border-gray-300"
                  style={{ backgroundColor: getColorValue(room.color) }}
                ></div>
                <div>
                  <h3 className="font-medium text-gray-900">{room.name}</h3>
                  <p className="text-sm text-gray-600">{room.paintType}</p>
                </div>
              </div>
              <div className="text-right">
                <p className="font-medium text-gray-900">{room.color}</p>
                <p className="text-sm text-gray-600">{room.area} sqft</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Color Detail Modal */}
      {selectedColor && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-2xl max-w-md w-full">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold text-gray-900">Color Details</h3>
                <button
                  onClick={() => setSelectedColor(null)}
                  className="p-2 text-gray-400 hover:text-gray-600 rounded-lg"
                >
                  ×
                </button>
              </div>
              
              <div className="text-center mb-6">
                <div
                  className="w-32 h-32 rounded-lg border-2 border-gray-200 shadow-sm mx-auto mb-4"
                  style={{ backgroundColor: getColorValue(selectedColor.name) }}
                ></div>
                <h4 className="text-2xl font-bold text-gray-900 mb-2">{selectedColor.name}</h4>
                <p className="text-gray-600">{selectedColor.category}</p>
              </div>

              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Room:</span>
                  <span className="font-medium text-gray-900">{selectedColor.room}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Category:</span>
                  <span className="font-medium text-gray-900">{selectedColor.category}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Color Code:</span>
                  <span className="font-medium text-gray-900">{getColorValue(selectedColor.name)}</span>
                </div>
              </div>

              <div className="mt-6 pt-6 border-t border-gray-200">
                <p className="text-sm text-gray-600 text-center">
                  This color has been selected for your {selectedColor.room.toLowerCase()} 
                  as part of your painting project.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ColoursPage;
